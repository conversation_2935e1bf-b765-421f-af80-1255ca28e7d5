const fs = require('fs');
const FormData = require('./backend/node_modules/form-data');
const fetch = require('./backend/node_modules/node-fetch');

async function testImportIngredients() {
  try {
    // Lire le fichier Excel
    const fileBuffer = fs.readFileSync('./test_ingredients.xlsx');
    
    // Créer FormData
    const formData = new FormData();
    formData.append('file', fileBuffer, {
      filename: 'test_ingredients.xlsx',
      contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    
    // Envoyer la requête à l'API
    const response = await fetch('http://localhost:8080/api/ingredient-import/cuisine/1', {
      method: 'POST',
      body: formData,
      headers: {
        ...formData.getHeaders()
      }
    });
    
    const result = await response.json();
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ Import réussi !');
      console.log(`Total: ${result.data.totalRows}`);
      console.log(`Succès: ${result.data.successCount}`);
      console.log(`Erreurs: ${result.data.errorCount}`);
    } else {
      console.log('\n❌ Import échoué');
    }
    
  } catch (error) {
    console.error('Erreur lors du test:', error.message);
  }
}

testImportIngredients();
