const XLSX = require('xlsx');
const BaseService = require('./base.service');
const logger = require('../logger');
const db = require('../db');

/**
 * Service d'import d'ingrédients et inventaire boissons depuis des fichiers Excel
 */
class IngredientImportService extends BaseService {

  /**
   * Import ingrédients cuisine depuis Excel
   */
  static async importCuisineIngredients(complexeId, excelBuffer, options = {}) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');
      
      // 1. Parser le fichier Excel
      const workbook = XLSX.read(excelBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      logger.info(`Parsing Excel file: ${data.length} rows found`);

      // 2. <PERSON><PERSON> les données
      const validationResult = this.validateCuisineIngredientData(data);
      if (!validationResult.isValid) {
        throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
      }

      // 3. Récupérer les informations du complexe
      const complexeResult = await client.query(
        'SELECT * FROM "ComplexesHoteliers" WHERE complexe_id = $1',
        [complexeId]
      );

      if (complexeResult.rows.length === 0) {
        throw new Error('Complexe non trouvé');
      }

      const complexe = complexeResult.rows[0];
      const { chaine_id } = complexe;

      // 4. Créer les ingrédients
      const importResults = [];
      let successCount = 0;
      let errorCount = 0;

      for (const row of data) {
        try {
          // Vérifier si l'ingrédient existe déjà
          const existingIngredient = await client.query(
            'SELECT * FROM "Ingredients" WHERE nom = $1 AND complexe_id = $2',
            [row.nom_ingredient, complexeId]
          );

          if (existingIngredient.rows.length > 0) {
            importResults.push({
              row: row,
              success: false,
              error: 'Ingrédient déjà existant',
              message: `Ingrédient "${row.nom_ingredient}" existe déjà`
            });
            errorCount++;
            continue;
          }

          const ingredientResult = await client.query(
            `INSERT INTO "Ingredients" (
              chaine_id, complexe_id, nom, description, unite_mesure, categorie,
              prix_unitaire_moyen, stock_initial, stock_minimal, stock_maximal,
              allergenes, conservation, duree_conservation_jours, actif, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW())
            RETURNING *`,
            [
              chaine_id,
              complexeId,
              row.nom_ingredient,
              row.description || '',
              row.unite_mesure || 'kg',
              row.categorie || 'Autre',
              parseFloat(row.prix_unitaire) || 0,
              parseFloat(row.stock_initial) || 0,
              parseFloat(row.stock_minimal) || 1,
              parseFloat(row.stock_maximal) || 100,
              row.allergenes ? JSON.stringify(row.allergenes.split(',').map(a => a.trim())) : null,
              row.conservation || 'Frais',
              parseInt(row.duree_conservation_jours) || 7,
              row.actif !== false
            ]
          );

          // Le stock initial est maintenant géré directement dans la table Ingredients
          // Plus besoin d'initialiser séparément le stock car il est inclus dans l'insertion principale
          logger.info(`Ingrédient créé: ${row.nom_ingredient} avec stock initial: ${parseFloat(row.stock_initial) || 0}`);

          importResults.push({
            row: row,
            success: true,
            ingredient: ingredientResult.rows[0],
            message: 'Ingrédient créé avec succès'
          });
          successCount++;

        } catch (error) {
          logger.error(`Error importing ingredient row:`, error);
          importResults.push({
            row: row,
            success: false,
            error: error.message,
            message: `Erreur: ${error.message}`
          });
          errorCount++;
        }
      }

      await client.query('COMMIT');

      const report = {
        totalRows: data.length,
        successCount,
        errorCount,
        results: importResults
      };

      logger.info(`Cuisine ingredients import completed: ${successCount} success, ${errorCount} errors`);

      return this.successResponse(report, 'Import des ingrédients cuisine terminé');

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error in importCuisineIngredients:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Import inventaire boissons depuis Excel
   */
  static async importBoissonInventory(complexeId, excelBuffer, options = {}) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');
      
      // 1. Parser le fichier Excel
      const workbook = XLSX.read(excelBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      logger.info(`Parsing Excel file: ${data.length} rows found`);

      // 2. Valider les données
      const validationResult = this.validateBoissonInventoryData(data);
      if (!validationResult.isValid) {
        throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
      }

      // 3. Récupérer les informations du complexe
      const complexeResult = await client.query(
        'SELECT * FROM "ComplexesHoteliers" WHERE complexe_id = $1',
        [complexeId]
      );

      if (complexeResult.rows.length === 0) {
        throw new Error('Complexe non trouvé');
      }

      const complexe = complexeResult.rows[0];
      const { chaine_id } = complexe;

      // 4. Créer les boissons dans l'inventaire
      const importResults = [];
      let successCount = 0;
      let errorCount = 0;

      for (const row of data) {
        try {
          // Vérifier si la boisson existe déjà
          const existingBoisson = await client.query(
            'SELECT * FROM "InventaireBoissons" WHERE nom = $1 AND complexe_id = $2',
            [row.nom_boisson, complexeId]
          );

          if (existingBoisson.rows.length > 0) {
            importResults.push({
              row: row,
              success: false,
              error: 'Boisson déjà existante',
              message: `Boisson "${row.nom_boisson}" existe déjà dans l'inventaire`
            });
            errorCount++;
            continue;
          }

          const boissonResult = await client.query(
            `INSERT INTO "InventaireBoissons" (
              chaine_id, complexe_id, nom, description, categorie, type_conditionnement,
              volume_unitaire, prix_achat_unitaire, prix_vente_unitaire, fournisseur,
              degre_alcool, code_barre, stock_minimal, stock_maximal,
              emplacement_stockage, temperature_stockage, actif, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, NOW())
            RETURNING *`,
            [
              chaine_id,
              complexeId,
              row.nom_boisson,
              row.description || '',
              row.categorie || 'Autre',
              row.type_conditionnement || 'Bouteille',
              parseFloat(row.volume_unitaire) || 0.33,
              parseFloat(row.prix_achat_unitaire) || 0,
              parseFloat(row.prix_vente_unitaire) || 0,
              row.fournisseur || '',
              parseFloat(row.degre_alcool) || 0,
              row.code_barre || '',
              parseInt(row.stock_minimal) || 1,
              parseInt(row.stock_maximal) || 100,
              row.emplacement_stockage || 'Cave',
              row.temperature_stockage || 'Frais',
              row.actif !== false
            ]
          );

          // Initialiser le stock avec le service dédié (plus robuste)
          const stockInitial = parseInt(row.stock_initial) || 0;
          const stockMinimal = parseInt(row.stock_minimal) || 1;
          const stockMaximal = parseInt(row.stock_maximal) || 100;

          if (stockInitial > 0 || stockMinimal > 0) {
            try {
              const StockManagementService = require('./stockManagement.service');
              await StockManagementService.initialiserStockBoisson(
                boissonResult.rows[0].boisson_id,
                complexeId,
                stockInitial,
                stockMinimal,
                stockMaximal,
                null // employeId sera null pour les imports automatiques
              );
            } catch (stockError) {
              logger.warn('Error initializing stock with StockManagementService, using fallback:', stockError.message);

              // Fallback: insertion directe si le service ne fonctionne pas
              try {
                await client.query(
                  `INSERT INTO "StockBoissons" (
                    boisson_id, complexe_id, quantite_actuelle, quantite_minimale,
                    quantite_maximale, derniere_mise_a_jour, created_at
                  ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())`,
                  [
                    boissonResult.rows[0].boisson_id,
                    complexeId,
                    stockInitial,
                    stockMinimal,
                    stockMaximal
                  ]
                );
              } catch (fallbackError) {
                logger.warn('StockBoissons table not found, skipping stock initialization');
              }
            }
          }

          importResults.push({
            row: row,
            success: true,
            boisson: boissonResult.rows[0],
            message: 'Boisson ajoutée à l\'inventaire avec succès'
          });
          successCount++;

        } catch (error) {
          logger.error(`Error importing boisson row:`, error);
          importResults.push({
            row: row,
            success: false,
            error: error.message,
            message: `Erreur: ${error.message}`
          });
          errorCount++;
        }
      }

      await client.query('COMMIT');

      const report = {
        totalRows: data.length,
        successCount,
        errorCount,
        results: importResults
      };

      logger.info(`Boisson inventory import completed: ${successCount} success, ${errorCount} errors`);

      return this.successResponse(report, 'Import de l\'inventaire boissons terminé');

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error in importBoissonInventory:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Validation des données ingrédients cuisine
   */
  static validateCuisineIngredientData(data) {
    const errors = [];
    const requiredFields = ['nom_ingredient', 'categorie', 'unite_mesure', 'prix_unitaire', 'stock_initial'];

    if (!Array.isArray(data) || data.length === 0) {
      errors.push('Aucune donnée trouvée dans le fichier');
      return { isValid: false, errors };
    }

    data.forEach((row, index) => {
      const rowNumber = index + 2;

      // Vérifier les champs obligatoires
      requiredFields.forEach(field => {
        if (!row[field] || row[field].toString().trim() === '') {
          errors.push(`Ligne ${rowNumber}: ${field} est obligatoire`);
        }
      });

      // Valider le prix unitaire
      if (row.prix_unitaire && isNaN(parseFloat(row.prix_unitaire))) {
        errors.push(`Ligne ${rowNumber}: prix_unitaire doit être un nombre`);
      }

      // Valider la durée de conservation
      if (row.duree_conservation_jours && isNaN(parseInt(row.duree_conservation_jours))) {
        errors.push(`Ligne ${rowNumber}: duree_conservation_jours doit être un nombre entier`);
      }

      // Valider le stock initial (obligatoire)
      if (!row.stock_initial || isNaN(parseFloat(row.stock_initial))) {
        errors.push(`Ligne ${rowNumber}: stock_initial est obligatoire et doit être un nombre`);
      }

      // Valider le stock minimal
      if (row.stock_minimal && isNaN(parseFloat(row.stock_minimal))) {
        errors.push(`Ligne ${rowNumber}: stock_minimal doit être un nombre`);
      }

      // Valider le stock maximal
      if (row.stock_maximal && isNaN(parseFloat(row.stock_maximal))) {
        errors.push(`Ligne ${rowNumber}: stock_maximal doit être un nombre`);
      }

      // Valider les unités de mesure
      const validUnits = ['kg', 'g', 'L', 'ml', 'pièce', 'sachet', 'boîte'];
      if (row.unite_mesure && !validUnits.includes(row.unite_mesure)) {
        errors.push(`Ligne ${rowNumber}: unite_mesure doit être une des valeurs: ${validUnits.join(', ')}`);
      }

      // Valider la conservation
      const validConservation = ['Frais', 'Sec', 'Congelé', 'Ambiante'];
      if (row.conservation && !validConservation.includes(row.conservation)) {
        errors.push(`Ligne ${rowNumber}: conservation doit être une des valeurs: ${validConservation.join(', ')}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validation des données inventaire boissons
   */
  static validateBoissonInventoryData(data) {
    const errors = [];
    const requiredFields = ['nom_boisson', 'categorie', 'type_conditionnement', 'volume_unitaire', 'stock_initial', 'stock_minimal'];

    if (!Array.isArray(data) || data.length === 0) {
      errors.push('Aucune donnée trouvée dans le fichier');
      return { isValid: false, errors };
    }

    data.forEach((row, index) => {
      const rowNumber = index + 2;

      // Vérifier les champs obligatoires
      requiredFields.forEach(field => {
        if (!row[field] || row[field].toString().trim() === '') {
          errors.push(`Ligne ${rowNumber}: ${field} est obligatoire`);
        }
      });

      // Valider les prix
      ['prix_achat_unitaire', 'prix_vente_unitaire'].forEach(field => {
        if (row[field] && isNaN(parseFloat(row[field]))) {
          errors.push(`Ligne ${rowNumber}: ${field} doit être un nombre`);
        }
      });

      // Valider le volume unitaire
      if (row.volume_unitaire && isNaN(parseFloat(row.volume_unitaire))) {
        errors.push(`Ligne ${rowNumber}: volume_unitaire doit être un nombre`);
      }

      // Valider le degré d'alcool
      if (row.degre_alcool && isNaN(parseFloat(row.degre_alcool))) {
        errors.push(`Ligne ${rowNumber}: degre_alcool doit être un nombre`);
      }

      // Valider les stocks
      ['stock_initial', 'stock_minimal', 'stock_maximal'].forEach(field => {
        if (row[field] && isNaN(parseInt(row[field]))) {
          errors.push(`Ligne ${rowNumber}: ${field} doit être un nombre entier`);
        }
      });

      // Valider les catégories
      const validCategories = ['Bière', 'Vin', 'Spiritueux', 'Cocktail', 'Soft', 'Alcool'];
      if (row.categorie && !validCategories.includes(row.categorie)) {
        errors.push(`Ligne ${rowNumber}: categorie doit être une des valeurs: ${validCategories.join(', ')}`);
      }

      // Valider les conditionnements
      const validConditionnements = ['Bouteille', 'Canette', 'Fût', 'Magnum', 'Jéroboam'];
      if (row.type_conditionnement && !validConditionnements.includes(row.type_conditionnement)) {
        errors.push(`Ligne ${rowNumber}: type_conditionnement doit être une des valeurs: ${validConditionnements.join(', ')}`);
      }

      // Valider les températures de stockage
      const validTemperatures = ['Ambiante', 'Frais', 'Très frais', 'Congelé'];
      if (row.temperature_stockage && !validTemperatures.includes(row.temperature_stockage)) {
        errors.push(`Ligne ${rowNumber}: temperature_stockage doit être une des valeurs: ${validTemperatures.join(', ')}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = IngredientImportService;
