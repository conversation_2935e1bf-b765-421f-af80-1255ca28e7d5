const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');
const BaseService = require('./base.service');
const logger = require('../logger');

/**
 * Service de génération de templates Excel pour l'import de menus et inventaires
 */
class TemplateGeneratorService extends BaseService {

  /**
   * Générer template Excel pour menu restaurant
   */
  static generateRestaurantTemplate() {
    try {
      const data = [
        // Headers
        [
          'nom_plat',
          'description', 
          'categorie',
          'prix_vente',
          'temps_preparation',
          'allergenes',
          'image_url',
          'actif'
        ],
        // Exemples
        [
          'Salade César',
          'Salade fraîche avec croûtons, parmesan et sauce César',
          'Entrée',
          12.50,
          15,
          'Gluten, Lait, Œufs',
          'https://example.com/salade-cesar.jpg',
          true
        ],
        [
          'Steak Frites',
          'Steak de bœuf grillé avec frites maison',
          'Plat Principal',
          25.00,
          20,
          '',
          '',
          true
        ],
        [
          '<PERSON><PERSON> Tatin',
          'Tarte aux pommes caramélisées',
          'Dessert',
          8.50,
          5,
          '<PERSON><PERSON>en, <PERSON><PERSON>, Œufs',
          '',
          true
        ],
        [
          'Coca-Cola',
          'Boisson gazeuse 33cl',
          '<PERSON>son',
          3.50,
          0,
          '',
          '',
          true
        ]
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(data);
      
      // Définir les largeurs de colonnes
      worksheet['!cols'] = [
        { width: 20 }, // nom_plat
        { width: 40 }, // description
        { width: 15 }, // categorie
        { width: 12 }, // prix_vente
        { width: 18 }, // temps_preparation
        { width: 25 }, // allergenes
        { width: 30 }, // image_url
        { width: 8 }   // actif
      ];

      // Ajouter des commentaires aux headers
      const comments = {
        'A1': 'Nom du plat (obligatoire)',
        'B1': 'Description détaillée du plat',
        'C1': 'Catégorie: Entrée, Plat Principal, Dessert, Boisson',
        'D1': 'Prix de vente en FCFA (obligatoire)',
        'E1': 'Temps de préparation en minutes',
        'F1': 'Allergènes séparés par des virgules',
        'G1': 'URL de l\'image (optionnel)',
        'H1': 'true ou false'
      };

      Object.keys(comments).forEach(cell => {
        if (!worksheet[cell]) worksheet[cell] = {};
        worksheet[cell].c = [{ t: comments[cell] }];
      });

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Menu Restaurant');

      return workbook;
    } catch (error) {
      logger.error('Error generating restaurant template:', error);
      throw error;
    }
  }

  /**
   * Générer template Excel pour carte bar
   */
  static generateBarTemplate() {
    try {
      const data = [
        // Headers
        [
          'nom_boisson',
          'description',
          'categorie', 
          'prix_vente',
          'degre_alcool',
          'volume_ml',
          'allergenes',
          'image_url',
          'actif'
        ],
        // Exemples
        [
          'Mojito',
          'Cocktail à base de rhum, menthe fraîche et citron vert',
          'Cocktail',
          12.00,
          15.0,
          250,
          '',
          '',
          true
        ],
        [
          'Heineken',
          'Bière blonde premium',
          'Bière',
          5.50,
          5.0,
          330,
          'Gluten',
          '',
          true
        ],
        [
          'Château Margaux 2015',
          'Vin rouge de Bordeaux',
          'Vin',
          85.00,
          13.5,
          750,
          'Sulfites',
          '',
          true
        ],
        [
          'Coca-Cola',
          'Boisson gazeuse',
          'Soft',
          3.50,
          0,
          330,
          '',
          '',
          true
        ],
        [
          'Whisky Macallan 18',
          'Whisky single malt écossais',
          'Spiritueux',
          25.00,
          43.0,
          40,
          '',
          '',
          true
        ]
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(data);
      
      // Définir les largeurs de colonnes
      worksheet['!cols'] = [
        { width: 20 }, // nom_boisson
        { width: 40 }, // description
        { width: 15 }, // categorie
        { width: 12 }, // prix_vente
        { width: 15 }, // degre_alcool
        { width: 12 }, // volume_ml
        { width: 25 }, // allergenes
        { width: 30 }, // image_url
        { width: 8 }   // actif
      ];

      // Ajouter des commentaires aux headers
      const comments = {
        'A1': 'Nom de la boisson (obligatoire)',
        'B1': 'Description de la boisson',
        'C1': 'Catégorie: Cocktail, Bière, Vin, Soft, Spiritueux, Alcool',
        'D1': 'Prix de vente en FCFA (obligatoire)',
        'E1': 'Degré d\'alcool (0 pour les softs)',
        'F1': 'Volume en millilitres',
        'G1': 'Allergènes séparés par des virgules',
        'H1': 'URL de l\'image (optionnel)',
        'I1': 'true ou false'
      };

      Object.keys(comments).forEach(cell => {
        if (!worksheet[cell]) worksheet[cell] = {};
        worksheet[cell].c = [{ t: comments[cell] }];
      });

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Carte Bar');

      return workbook;
    } catch (error) {
      logger.error('Error generating bar template:', error);
      throw error;
    }
  }

  /**
   * Générer template Excel pour ingrédients cuisine
   */
  static generateCuisineIngredientTemplate() {
    try {
      const data = [
        // Headers
        [
          'nom_ingredient',
          'description',
          'categorie',
          'unite_mesure',
          'prix_unitaire',
          'stock_initial',
          'stock_minimal',
          'stock_maximal',
          'fournisseur',
          'allergenes',
          'conservation',
          'duree_conservation_jours',
          'actif'
        ],
        // Exemples
        [
          'Tomates fraîches',
          'Tomates de saison',
          'Légume',
          'kg',
          4.50,
          25.0,  // stock_initial
          5.0,   // stock_minimal
          50.0,  // stock_maximal
          'Marché Central',
          '',
          'Frais',
          7,
          true
        ],
        [
          'Farine de blé',
          'Farine T55 pour pâtisserie',
          'Céréale',
          'kg',
          2.20,
          50.0,  // stock_initial
          10.0,  // stock_minimal
          100.0, // stock_maximal
          'Minoterie Locale',
          'Gluten',
          'Sec',
          365,
          true
        ],
        [
          'Bœuf (entrecôte)',
          'Viande de bœuf première qualité',
          'Viande',
          'kg',
          18.00,
          15.0,  // stock_initial
          2.0,   // stock_minimal
          30.0,  // stock_maximal
          'Boucherie Martin',
          '',
          'Frais',
          3,
          true
        ],
        [
          'Huile d\'olive',
          'Huile d\'olive extra vierge',
          'Matière grasse',
          'L',
          12.50,
          10.0,  // stock_initial
          3.0,   // stock_minimal
          20.0,  // stock_maximal
          'Épicerie Fine',
          '',
          'Sec',
          730,
          true
        ]
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(data);
      
      // Définir les largeurs de colonnes
      worksheet['!cols'] = [
        { width: 20 }, // nom_ingredient
        { width: 30 }, // description
        { width: 15 }, // categorie
        { width: 15 }, // unite_mesure
        { width: 15 }, // prix_unitaire
        { width: 15 }, // stock_initial
        { width: 15 }, // stock_minimal
        { width: 15 }, // stock_maximal
        { width: 20 }, // fournisseur
        { width: 20 }, // allergenes
        { width: 15 }, // conservation
        { width: 25 }, // duree_conservation_jours
        { width: 8 }   // actif
      ];

      // Ajouter des commentaires aux headers
      const comments = {
        'A1': 'Nom de l\'ingrédient (obligatoire)',
        'B1': 'Description de l\'ingrédient',
        'C1': 'Catégorie: Viande, Légume, Épice, Céréale, etc.',
        'D1': 'Unité: kg, L, pièce, g, etc.',
        'E1': 'Prix d\'achat unitaire en FCFA (obligatoire)',
        'F1': 'Stock de départ lors de l\'import (obligatoire)',
        'G1': 'Stock minimal d\'alerte (obligatoire)',
        'H1': 'Stock maximal recommandé',
        'I1': 'Nom du fournisseur',
        'J1': 'Allergènes séparés par des virgules',
        'K1': 'Conservation: Frais, Sec, Congelé',
        'L1': 'Durée de conservation en jours',
        'M1': 'true ou false'
      };

      Object.keys(comments).forEach(cell => {
        if (!worksheet[cell]) worksheet[cell] = {};
        worksheet[cell].c = [{ t: comments[cell] }];
      });

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Ingrédients Cuisine');

      return workbook;
    } catch (error) {
      logger.error('Error generating cuisine ingredient template:', error);
      throw error;
    }
  }

  /**
   * Générer template Excel pour les liens produits-ingrédients
   */
  static generateProduitsIngredientsTemplate() {
    try {
      const data = [
        // Headers
        [
          'nom_produit',
          'nom_ingredient',
          'quantite_necessaire',
          'unite_mesure',
          'cout_unitaire',
          'optionnel',
          'notes'
        ],
        // Exemples
        [
          'Salade César',
          'Salade verte',
          100,
          'g',
          0.50,
          false,
          'Base de la salade'
        ],
        [
          'Salade César',
          'Croûtons',
          30,
          'g',
          0.20,
          false,
          'Pour le croquant'
        ],
        [
          'Salade César',
          'Parmesan',
          25,
          'g',
          1.50,
          false,
          'Fromage râpé'
        ],
        [
          'Steak Frites',
          'Bœuf (entrecôte)',
          200,
          'g',
          3.60,
          false,
          'Pièce principale'
        ],
        [
          'Steak Frites',
          'Pommes de terre',
          300,
          'g',
          0.90,
          false,
          'Pour les frites'
        ],
        [
          'Steak Frites',
          'Huile d\'olive',
          20,
          'ml',
          0.25,
          false,
          'Pour la cuisson'
        ]
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(data);

      // Définir les largeurs de colonnes
      worksheet['!cols'] = [
        { width: 25 }, // nom_produit
        { width: 25 }, // nom_ingredient
        { width: 18 }, // quantite_necessaire
        { width: 15 }, // unite_mesure
        { width: 15 }, // cout_unitaire
        { width: 12 }, // optionnel
        { width: 30 }  // notes
      ];

      // Ajouter des commentaires aux headers
      const comments = {
        'A1': 'Nom du produit (doit exister dans le menu)',
        'B1': 'Nom de l\'ingrédient (doit exister dans la liste)',
        'C1': 'Quantité nécessaire par portion',
        'D1': 'Unité de mesure (g, ml, pièce, etc.)',
        'E1': 'Coût unitaire (optionnel, prix de l\'ingrédient par défaut)',
        'F1': 'true/false - Si l\'ingrédient est optionnel',
        'G1': 'Notes sur l\'utilisation de cet ingrédient'
      };

      Object.keys(comments).forEach(cell => {
        if (!worksheet[cell]) worksheet[cell] = {};
        worksheet[cell].c = [{ t: comments[cell] }];
      });

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Produits-Ingrédients');

      return workbook;
    } catch (error) {
      logger.error('Error generating produits-ingredients template:', error);
      throw error;
    }
  }

  /**
   * Générer template Excel pour menu restaurant avec ingrédients
   */
  static generateRestaurantMenuWithIngredientsTemplate() {
    try {
      const data = [
        // Headers
        [
          'nom_plat',
          'description',
          'categorie',
          'prix_vente',
          'temps_preparation',
          'allergenes',
          'ingredients',
          'image_url',
          'actif'
        ],
        // Exemples
        [
          'Salade César',
          'Salade fraîche avec croûtons, parmesan et sauce César',
          'Entrée',
          12.50,
          15,
          'Gluten, Lait, Œufs',
          'Salade verte:100:g,Croûtons:30:g,Parmesan:25:g,Sauce César:50:ml',
          'https://example.com/salade-cesar.jpg',
          true
        ],
        [
          'Steak Frites',
          'Steak de bœuf grillé avec frites maison',
          'Plat Principal',
          25.00,
          20,
          '',
          'Steak de bœuf:200:g,Pommes de terre:300:g,Huile de friture:50:ml,Sel:2:g',
          '',
          true
        ],
        [
          'Tarte Tatin',
          'Tarte aux pommes caramélisées',
          'Dessert',
          8.50,
          5,
          'Gluten, Lait, Œufs',
          'Pâte brisée:150:g,Pommes:200:g,Sucre:50:g,Beurre:30:g',
          '',
          true
        ]
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(data);

      // Définir les largeurs de colonnes
      worksheet['!cols'] = [
        { width: 20 }, // nom_plat
        { width: 40 }, // description
        { width: 15 }, // categorie
        { width: 12 }, // prix_vente
        { width: 18 }, // temps_preparation
        { width: 25 }, // allergenes
        { width: 60 }, // ingredients
        { width: 30 }, // image_url
        { width: 8 }   // actif
      ];

      // Ajouter des commentaires aux headers
      const comments = {
        'A1': 'Nom du plat (obligatoire)',
        'B1': 'Description détaillée du plat',
        'C1': 'Catégorie: Entrée, Plat Principal, Dessert, Boisson',
        'D1': 'Prix de vente en FCFA (obligatoire)',
        'E1': 'Temps de préparation en minutes',
        'F1': 'Allergènes séparés par des virgules',
        'G1': 'Ingrédients: nom:quantité:unité,nom2:quantité:unité',
        'H1': 'URL de l\'image (optionnel)',
        'I1': 'true ou false'
      };

      Object.keys(comments).forEach(cell => {
        if (!worksheet[cell]) worksheet[cell] = {};
        worksheet[cell].c = [{ t: comments[cell] }];
      });

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Menu Restaurant');

      return workbook;
    } catch (error) {
      logger.error('Error generating restaurant menu with ingredients template:', error);
      throw error;
    }
  }

  /**
   * Générer template Excel pour carte bar avec ingrédients
   */
  static generateBarMenuWithIngredientsTemplate() {
    try {
      const data = [
        // Headers
        [
          'nom_boisson',
          'description',
          'categorie',
          'prix_vente',
          'degre_alcool',
          'volume_ml',
          'ingredients',
          'allergenes',
          'image_url',
          'actif'
        ],
        // Exemples
        [
          'Mojito',
          'Cocktail à base de rhum blanc, menthe et citron vert',
          'Cocktail',
          8.50,
          12.5,
          250,
          'Rhum blanc:50:ml,Menthe fraîche:10:g,Citron vert:30:ml,Sucre de canne:10:g,Eau gazeuse:150:ml',
          '',
          '',
          true
        ],
        [
          'Heineken',
          'Bière blonde premium',
          'Bière',
          4.50,
          5.0,
          330,
          'Bière Heineken:330:ml',
          'Gluten',
          '',
          true
        ],
        [
          'Whisky Coca',
          'Whisky avec Coca-Cola et glaçons',
          'Long Drink',
          9.00,
          15.0,
          300,
          'Whisky:40:ml,Coca-Cola:200:ml,Glaçons:60:g',
          '',
          '',
          true
        ]
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(data);

      // Définir les largeurs de colonnes
      worksheet['!cols'] = [
        { width: 20 }, // nom_boisson
        { width: 40 }, // description
        { width: 15 }, // categorie
        { width: 12 }, // prix_vente
        { width: 15 }, // degre_alcool
        { width: 12 }, // volume_ml
        { width: 60 }, // ingredients
        { width: 25 }, // allergenes
        { width: 30 }, // image_url
        { width: 8 }   // actif
      ];

      // Ajouter des commentaires aux headers
      const comments = {
        'A1': 'Nom de la boisson (obligatoire)',
        'B1': 'Description de la boisson',
        'C1': 'Catégorie: Cocktail, Bière, Vin, Spiritueux, etc.',
        'D1': 'Prix de vente en FCFA (obligatoire)',
        'E1': 'Degré d\'alcool en %',
        'F1': 'Volume en ml',
        'G1': 'Ingrédients: nom:quantité:unité,nom2:quantité:unité',
        'H1': 'Allergènes séparés par des virgules',
        'I1': 'URL de l\'image (optionnel)',
        'J1': 'true ou false'
      };

      Object.keys(comments).forEach(cell => {
        if (!worksheet[cell]) worksheet[cell] = {};
        worksheet[cell].c = [{ t: comments[cell] }];
      });

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Carte Bar');

      return workbook;
    } catch (error) {
      logger.error('Error generating bar menu with ingredients template:', error);
      throw error;
    }
  }

  /**
   * Générer template Excel pour inventaire boissons
   */
  static generateBoissonInventoryTemplate() {
    try {
      const data = [
        // Headers
        [
          'nom_boisson',
          'description',
          'categorie',
          'type_conditionnement',
          'volume_unitaire',
          'prix_achat_unitaire',
          'prix_vente_unitaire',
          'fournisseur',
          'degre_alcool',
          'code_barre',
          'stock_initial',
          'stock_minimal',
          'stock_maximal',
          'emplacement_stockage',
          'temperature_stockage',
          'actif'
        ],
        // Exemples
        [
          'Heineken',
          'Bière blonde premium',
          'Bière',
          'Bouteille',
          0.33,
          2.50,
          5.50,
          'Distributeur Boissons SA',
          5.0,
          '8712000043209',
          100,  // stock_initial
          24,   // stock_minimal
          200,  // stock_maximal
          'Cave',
          'Frais',
          true
        ],
        [
          'Château Margaux 2015',
          'Vin rouge de Bordeaux',
          'Vin',
          'Bouteille',
          0.75,
          45.00,
          85.00,
          'Caviste Premium',
          13.5,
          '',
          25,   // stock_initial
          6,    // stock_minimal
          50,   // stock_maximal
          'Cave à vin',
          'Très frais',
          true
        ],
        [
          'Rhum Havana Club 7 ans',
          'Rhum cubain vieilli',
          'Spiritueux',
          'Bouteille',
          0.70,
          18.00,
          35.00,
          'Distributeur Alcools',
          40.0,
          '',
          10,   // stock_initial
          3,    // stock_minimal
          20,   // stock_maximal
          'Bar',
          'Ambiante',
          true
        ],
        [
          'Coca-Cola',
          'Boisson gazeuse',
          'Soft',
          'Canette',
          0.33,
          1.20,
          3.50,
          'Distributeur Softs',
          0,
          '5449000000996',
          200,  // stock_initial
          48,   // stock_minimal
          500,  // stock_maximal
          'Réserve',
          'Frais',
          true
        ]
      ];

      const worksheet = XLSX.utils.aoa_to_sheet(data);
      
      // Définir les largeurs de colonnes
      worksheet['!cols'] = [
        { width: 20 }, // nom_boisson
        { width: 30 }, // description
        { width: 15 }, // categorie
        { width: 20 }, // type_conditionnement
        { width: 18 }, // volume_unitaire
        { width: 20 }, // prix_achat_unitaire
        { width: 20 }, // prix_vente_unitaire
        { width: 25 }, // fournisseur
        { width: 15 }, // degre_alcool
        { width: 15 }, // code_barre
        { width: 15 }, // stock_initial
        { width: 15 }, // stock_minimal
        { width: 15 }, // stock_maximal
        { width: 20 }, // emplacement_stockage
        { width: 20 }, // temperature_stockage
        { width: 8 }   // actif
      ];

      // Ajouter des commentaires aux headers
      const comments = {
        'A1': 'Nom de la boisson (obligatoire)',
        'B1': 'Description de la boisson',
        'C1': 'Catégorie: Bière, Vin, Spiritueux, Cocktail, Soft, Alcool',
        'D1': 'Type de conditionnement: Bouteille, Canette, Fût, Magnum',
        'E1': 'Volume unitaire en litres (ex: 0.33, 0.75)',
        'F1': 'Prix d\'achat unitaire en FCFA',
        'G1': 'Prix de vente unitaire en FCFA',
        'H1': 'Nom du fournisseur',
        'I1': 'Degré d\'alcool en % (0 pour les softs)',
        'J1': 'Code barre (optionnel)',
        'K1': 'Stock initial lors de l\'import (obligatoire)',
        'L1': 'Stock minimal d\'alerte (obligatoire)',
        'M1': 'Stock maximal recommandé',
        'N1': 'Emplacement de stockage',
        'O1': 'Température: Ambiante, Frais, Très frais, Congelé',
        'P1': 'true ou false'
      };

      Object.keys(comments).forEach(cell => {
        if (!worksheet[cell]) worksheet[cell] = {};
        worksheet[cell].c = [{ t: comments[cell] }];
      });

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Inventaire Boissons');

      return workbook;
    } catch (error) {
      logger.error('Error generating boisson inventory template:', error);
      throw error;
    }
  }

  /**
   * Sauvegarder un workbook Excel dans le dossier templates
   */
  static async saveTemplate(workbook, filename) {
    try {
      const templatesDir = path.join(__dirname, '../templates');
      
      // Créer le dossier templates s'il n'existe pas
      if (!fs.existsSync(templatesDir)) {
        fs.mkdirSync(templatesDir, { recursive: true });
      }

      const filepath = path.join(templatesDir, filename);
      XLSX.writeFile(workbook, filepath);
      
      logger.info(`Template saved: ${filepath}`);
      return filepath;
    } catch (error) {
      logger.error(`Error saving template ${filename}:`, error);
      throw error;
    }
  }

  /**
   * Générer tous les templates
   */
  static async generateAllTemplates() {
    try {
      // Templates utilisés dans le workflow de configuration des services
      const templates = [
        {
          generator: this.generateRestaurantMenuWithIngredientsTemplate,
          filename: 'restaurant-menu-with-ingredients-template.xlsx'
        },
        {
          generator: this.generateBarMenuWithIngredientsTemplate,
          filename: 'bar-menu-with-ingredients-template.xlsx'
        },
        {
          generator: this.generateCuisineIngredientTemplate,
          filename: 'cuisine-ingredients-template.xlsx'
        },
        {
          generator: this.generateBoissonInventoryTemplate,
          filename: 'boisson-inventory-template.xlsx'
        }
      ];

      const results = [];
      for (const template of templates) {
        const workbook = template.generator();
        const filepath = await this.saveTemplate(workbook, template.filename);
        results.push({
          filename: template.filename,
          filepath,
          success: true
        });
      }

      logger.info('All templates generated successfully');
      return this.successResponse(results, 'Templates générés avec succès');
    } catch (error) {
      logger.error('Error generating templates:', error);
      throw error;
    }
  }

  /**
   * Obtenir le chemin d'un template
   */
  static getTemplatePath(filename) {
    return path.join(__dirname, '../templates', filename);
  }

  /**
   * Vérifier si un template existe
   */
  static templateExists(filename) {
    const filepath = this.getTemplatePath(filename);
    return fs.existsSync(filepath);
  }
}

module.exports = TemplateGeneratorService;
