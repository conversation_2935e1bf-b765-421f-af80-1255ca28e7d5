const db = require('../db');
const logger = require('../logger');

/**
 * Service d'intégration POS avec le système d'inventaire
 * Gère la déduction automatique du stock lors des ventes
 */
class POSStockIntegration {
  
  // Méthodes utilitaires
  static successResponse(data) {
    return {
      success: true,
      data
    };
  }

  static rejectResponse(message, code = 500) {
    return {
      success: false,
      message,
      code
    };
  }

  /**
   * Traitement d'une transaction POS avec déduction automatique du stock
   */
  static async processTransactionWithStock(transactionData) {
    try {
      await db.query('BEGIN');

      const {
        transaction_id,
        lignes_transaction = []
      } = transactionData;

      // Vérifier que la transaction existe
      const transactionResult = await db.query(
        'SELECT * FROM "TransactionsPOS" WHERE transaction_id = $1',
        [transaction_id]
      );

      if (transactionResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Transaction non trouvée', 404);
      }

      const transaction = transactionResult.rows[0];
      const stockUpdates = [];
      const alertes = [];

      // Traiter chaque ligne de transaction
      for (const ligne of lignes_transaction) {
        const { produit_id, quantite } = ligne;

        // Vérifier la disponibilité des ingrédients pour ce produit
        const disponibiliteCheck = await this.checkIngredientAvailability(
          produit_id, 
          quantite, 
          transaction.complexe_id
        );

        if (!disponibiliteCheck.success) {
          await db.query('ROLLBACK');
          return disponibiliteCheck;
        }

        // Si des ingrédients sont insuffisants, ajouter aux alertes
        if (disponibiliteCheck.data.alertes.length > 0) {
          alertes.push(...disponibiliteCheck.data.alertes);
        }

        // Préparer les mises à jour de stock
        stockUpdates.push({
          produit_id,
          quantite,
          ingredients: disponibiliteCheck.data.ingredients_requis
        });
      }

      // Effectuer les déductions de stock
      for (const update of stockUpdates) {
        await this.updateStockFromSale(
          update.produit_id,
          update.quantite,
          update.ingredients,
          transaction_id,
          transaction.complexe_id
        );
      }

      // Finaliser la transaction
      await db.query(
        'UPDATE "TransactionsPOS" SET statut = $1, updated_at = NOW() WHERE transaction_id = $2',
        ['Finalisée', transaction_id]
      );

      await db.query('COMMIT');

      logger.info('Transaction POS traitée avec déduction stock', {
        transaction_id,
        produits_traites: stockUpdates.length,
        alertes_generees: alertes.length
      });

      return this.successResponse({
        transaction_id,
        statut: 'Finalisée',
        stock_updates: stockUpdates.length,
        alertes,
        message: 'Transaction traitée avec succès'
      });

    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur traitement transaction avec stock:', error);
      return this.rejectResponse('Erreur lors du traitement de la transaction');
    }
  }

  /**
   * Vérifier la disponibilité des ingrédients pour un produit
   */
  static async checkIngredientAvailability(produitId, quantite, complexeId = null) {
    try {
      // Récupérer les ingrédients du produit via ProduitsIngredients
      const ingredientsResult = await db.query(`
        SELECT
          pi.ingredient_id,
          pi.quantite_necessaire,
          pi.unite_mesure,
          pi.cout_unitaire,
          i.nom as ingredient_nom,
          i.stock_actuel,
          i.stock_minimal,
          i.actif
        FROM "ProduitsIngredients" pi
        JOIN "Ingredients" i ON pi.ingredient_id = i.ingredient_id
        WHERE pi.produit_id = $1 AND i.actif = true
      `, [produitId]);

      const ingredientsData = ingredientsResult.rows;

      if (ingredientsData.length === 0) {
        // Pas d'ingrédients = pas de déduction de stock
        return this.successResponse({
          disponible: true,
          ingredients_requis: [],
          alertes: [],
          message: 'Aucun ingrédient trouvé pour ce produit, pas de déduction de stock'
        });
      }

      const ingredientsRequis = [];
      const alertes = [];

      // Vérifier chaque ingrédient
      for (const item of ingredientsData) {
        if (item.ingredient_id) {
          const quantiteNecessaire = item.quantite_necessaire * quantite;
          const stockActuel = item.stock_actuel || 0;

          ingredientsRequis.push({
            ingredient_id: item.ingredient_id,
            nom: item.ingredient_nom,
            quantite_necessaire: quantiteNecessaire,
            unite_mesure: item.unite_mesure,
            stock_actuel: stockActuel
          });

          // Vérifier si le stock est suffisant
          if (stockActuel < quantiteNecessaire) {
            return this.rejectResponse(
              `Stock insuffisant pour l'ingrédient "${item.ingredient_nom}". ` +
              `Requis: ${quantiteNecessaire} ${item.unite_mesure}, ` +
              `Disponible: ${stockActuel} ${item.unite_mesure}`,
              400
            );
          }

          // Vérifier si on va passer sous le stock minimal
          const stockApres = stockActuel - quantiteNecessaire;
          if (stockApres <= item.stock_minimal) {
            alertes.push({
              type: 'stock_faible',
              ingredient_id: item.ingredient_id,
              ingredient_nom: item.ingredient_nom,
              stock_actuel: stockActuel,
              stock_apres: stockApres,
              stock_minimal: item.stock_minimal,
              message: `Stock faible après vente: ${item.ingredient_nom}`
            });
          }
        }
      }

      return this.successResponse({
        disponible: true,
        ingredients_requis: ingredientsRequis,
        alertes,
        message: 'Vérification terminée'
      });

    } catch (error) {
      logger.error('Erreur vérification disponibilité ingrédients:', error);
      return this.rejectResponse('Erreur lors de la vérification des ingrédients');
    }
  }

  /**
   * Mettre à jour le stock après une vente
   */
  static async updateStockFromSale(produitId, quantite, ingredients, transactionId, complexeId) {
    try {
      // Déduire le stock de chaque ingrédient
      for (const ingredient of ingredients) {
        // Mettre à jour le stock de l'ingrédient
        await db.query(`
          UPDATE "Ingredients" 
          SET stock_actuel = stock_actuel - $1, updated_at = NOW() 
          WHERE ingredient_id = $2
        `, [ingredient.quantite_necessaire, ingredient.ingredient_id]);

        // Créer un mouvement de stock dans la table des ingrédients
        await db.query(`
          INSERT INTO "MouvementsStockIngredients" (
            ingredient_id,
            complexe_id,
            type_mouvement,
            quantite,
            quantite_avant,
            quantite_apres,
            prix_unitaire,
            reference_id,
            reference_type,
            employe_id,
            notes,
            date_mouvement
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW())
        `, [
          ingredient.ingredient_id,
          complexeId,
          'CONSOMMATION',
          ingredient.quantite_necessaire, // Quantité consommée (positive)
          ingredient.stock_avant || 0,
          (ingredient.stock_avant || 0) - ingredient.quantite_necessaire,
          ingredient.prix_unitaire || 0,
          transactionId,
          'TRANSACTION',
          null, // TODO: récupérer l'employé de la transaction
          `Vente produit: ${quantite} unité(s)`
        ]);
      }

      logger.info('Stock mis à jour après vente', {
        produit_id: produitId,
        quantite,
        ingredients_deduits: ingredients.length,
        transaction_id: transactionId
      });

      return true;

    } catch (error) {
      logger.error('Erreur mise à jour stock après vente:', error);
      throw error;
    }
  }

  /**
   * Générer des alertes de stock faible
   */
  static async generateStockAlerts(complexeId) {
    try {
      const alertesResult = await db.query(`
        SELECT 
          i.ingredient_id,
          i.nom,
          i.stock_actuel,
          i.stock_minimal,
          i.unite_mesure,
          i.categorie,
          CASE 
            WHEN i.stock_actuel <= 0 THEN 'RUPTURE'
            WHEN i.stock_actuel <= i.stock_minimal THEN 'STOCK_FAIBLE'
            ELSE 'OK'
          END as niveau_alerte
        FROM "Ingredients" i
        WHERE i.complexe_id = $1 
          AND i.actif = true 
          AND i.stock_actuel <= i.stock_minimal
        ORDER BY 
          CASE 
            WHEN i.stock_actuel <= 0 THEN 1
            WHEN i.stock_actuel <= i.stock_minimal THEN 2
            ELSE 3
          END,
          i.nom
      `, [complexeId]);

      const alertes = alertesResult.rows.map(row => ({
        ingredient_id: row.ingredient_id,
        nom: row.nom,
        stock_actuel: row.stock_actuel,
        stock_minimal: row.stock_minimal,
        unite_mesure: row.unite_mesure,
        categorie: row.categorie,
        niveau_alerte: row.niveau_alerte,
        message: row.niveau_alerte === 'RUPTURE' 
          ? `Rupture de stock: ${row.nom}`
          : `Stock faible: ${row.nom} (${row.stock_actuel}/${row.stock_minimal} ${row.unite_mesure})`
      }));

      return this.successResponse({
        alertes,
        total: alertes.length,
        ruptures: alertes.filter(a => a.niveau_alerte === 'RUPTURE').length,
        stocks_faibles: alertes.filter(a => a.niveau_alerte === 'STOCK_FAIBLE').length
      });

    } catch (error) {
      logger.error('Erreur génération alertes stock:', error);
      return this.rejectResponse('Erreur lors de la génération des alertes');
    }
  }

  /**
   * Calculer la consommation réelle d'ingrédients
   */
  static async calculateIngredientConsumption(complexeId, dateDebut, dateFin) {
    try {
      const consommationResult = await db.query(`
        SELECT 
          i.ingredient_id,
          i.nom as ingredient_nom,
          i.unite_mesure,
          i.categorie,
          SUM(ABS(ms.quantite)) as quantite_consommee,
          COUNT(ms.mouvement_id) as nombre_mouvements,
          AVG(ABS(ms.quantite)) as consommation_moyenne
        FROM "Ingredients" i
        JOIN "MouvementsStock" ms ON i.ingredient_id = ms.ingredient_id
        WHERE i.complexe_id = $1 
          AND ms.type_mouvement = 'SORTIE_VENTE'
          AND ms.created_at BETWEEN $2 AND $3
        GROUP BY i.ingredient_id, i.nom, i.unite_mesure, i.categorie
        ORDER BY quantite_consommee DESC
      `, [complexeId, dateDebut, dateFin]);

      const consommation = consommationResult.rows;

      return this.successResponse({
        periode: { debut: dateDebut, fin: dateFin },
        consommation,
        total_ingredients: consommation.length,
        consommation_totale: consommation.reduce((sum, item) => sum + parseFloat(item.quantite_consommee), 0)
      });

    } catch (error) {
      logger.error('Erreur calcul consommation ingrédients:', error);
      return this.rejectResponse('Erreur lors du calcul de la consommation');
    }
  }

  /**
   * Prévisions de rupture de stock basées sur les ventes
   */
  static async predictStockBreakdown(complexeId, joursPrevisionnels = 7) {
    try {
      // Calculer la consommation moyenne des 30 derniers jours
      const dateFin = new Date();
      const dateDebut = new Date();
      dateDebut.setDate(dateFin.getDate() - 30);

      const consommationResult = await db.query(`
        SELECT
          i.ingredient_id,
          i.nom as ingredient_nom,
          i.stock_actuel,
          i.unite_mesure,
          AVG(ABS(msi.quantite)) as consommation_moyenne_quotidienne
        FROM "Ingredients" i
        LEFT JOIN "MouvementsStockIngredients" msi ON i.ingredient_id = msi.ingredient_id
          AND msi.type_mouvement = 'CONSOMMATION'
          AND msi.date_mouvement BETWEEN $2 AND $3
        WHERE i.complexe_id = $1 AND i.actif = true
        GROUP BY i.ingredient_id, i.nom, i.stock_actuel, i.unite_mesure
        HAVING i.stock_actuel > 0
        ORDER BY i.nom
      `, [complexeId, dateDebut, dateFin]);

      const previsions = consommationResult.rows.map(row => {
        const consommationQuotidienne = parseFloat(row.consommation_moyenne_quotidienne) || 0;
        const stockActuel = parseFloat(row.stock_actuel) || 0;

        let joursRestants = null;
        let dateRuptureEstimee = null;
        let statut = 'OK';

        if (consommationQuotidienne > 0) {
          joursRestants = Math.floor(stockActuel / consommationQuotidienne);

          if (joursRestants <= joursPrevisionnels) {
            dateRuptureEstimee = new Date();
            dateRuptureEstimee.setDate(dateRuptureEstimee.getDate() + joursRestants);

            if (joursRestants <= 1) {
              statut = 'RUPTURE_IMMINENTE';
            } else if (joursRestants <= 3) {
              statut = 'ALERTE_ROUGE';
            } else {
              statut = 'ALERTE_ORANGE';
            }
          }
        }

        return {
          ingredient_id: row.ingredient_id,
          nom: row.ingredient_nom,
          stock_actuel: stockActuel,
          unite_mesure: row.unite_mesure,
          consommation_quotidienne: consommationQuotidienne,
          jours_restants: joursRestants,
          date_rupture_estimee: dateRuptureEstimee,
          statut
        };
      });

      // Filtrer seulement les ingrédients à risque
      const ingredientsARisque = previsions.filter(p => p.statut !== 'OK');

      return this.successResponse({
        periode_analyse: { debut: dateDebut, fin: dateFin },
        jours_previsionnels: joursPrevisionnels,
        ingredients_a_risque: ingredientsARisque,
        total_ingredients_analyses: previsions.length,
        repartition: {
          rupture_imminente: ingredientsARisque.filter(i => i.statut === 'RUPTURE_IMMINENTE').length,
          alerte_rouge: ingredientsARisque.filter(i => i.statut === 'ALERTE_ROUGE').length,
          alerte_orange: ingredientsARisque.filter(i => i.statut === 'ALERTE_ORANGE').length
        }
      });

    } catch (error) {
      logger.error('Erreur prévisions rupture stock:', error);
      return this.rejectResponse('Erreur lors du calcul des prévisions');
    }
  }

  /**
   * Optimisation des commandes basée sur la consommation
   */
  static async suggestOptimalOrders(complexeId, joursCommande = 14) {
    try {
      // Récupérer les données de consommation et stock
      const dateFin = new Date();
      const dateDebut = new Date();
      dateDebut.setDate(dateFin.getDate() - 30);

      const suggestionResult = await db.query(`
        SELECT
          i.ingredient_id,
          i.nom as ingredient_nom,
          i.stock_actuel,
          i.stock_minimal,
          i.stock_maximal,
          i.unite_mesure,
          i.prix_unitaire_moyen,
          i.fournisseur_principal_id,
          AVG(ABS(msi.quantite)) as consommation_moyenne_quotidienne,
          COUNT(msi.mouvement_id) as nombre_mouvements
        FROM "Ingredients" i
        LEFT JOIN "MouvementsStockIngredients" msi ON i.ingredient_id = msi.ingredient_id
          AND msi.type_mouvement = 'CONSOMMATION'
          AND msi.date_mouvement BETWEEN $2 AND $3
        WHERE i.complexe_id = $1 AND i.actif = true
        GROUP BY i.ingredient_id, i.nom, i.stock_actuel, i.stock_minimal,
                 i.stock_maximal, i.unite_mesure, i.prix_unitaire_moyen, i.fournisseur_principal_id
        ORDER BY i.nom
      `, [complexeId, dateDebut, dateFin]);

      const suggestions = suggestionResult.rows.map(row => {
        const consommationQuotidienne = parseFloat(row.consommation_moyenne_quotidienne) || 0;
        const stockActuel = parseFloat(row.stock_actuel) || 0;
        const stockMinimal = parseFloat(row.stock_minimal) || 0;
        const stockMaximal = parseFloat(row.stock_maximal) || stockMinimal * 3;

        // Calculer la quantité nécessaire pour la période
        const consommationPeriode = consommationQuotidienne * joursCommande;
        const stockCible = Math.max(stockMaximal, consommationPeriode + stockMinimal);
        const quantiteACommander = Math.max(0, stockCible - stockActuel);

        let priorite = 'BASSE';
        if (stockActuel <= stockMinimal) {
          priorite = 'HAUTE';
        } else if (stockActuel <= stockMinimal * 1.5) {
          priorite = 'MOYENNE';
        }

        const coutEstime = quantiteACommander * (parseFloat(row.prix_unitaire_moyen) || 0);

        return {
          ingredient_id: row.ingredient_id,
          nom: row.ingredient_nom,
          stock_actuel: stockActuel,
          stock_minimal: stockMinimal,
          stock_maximal: stockMaximal,
          unite_mesure: row.unite_mesure,
          consommation_quotidienne: consommationQuotidienne,
          consommation_periode: consommationPeriode,
          quantite_a_commander: Math.round(quantiteACommander * 100) / 100,
          cout_estime: Math.round(coutEstime * 100) / 100,
          priorite,
          fournisseur_principal_id: row.fournisseur_principal_id,
          recommandation: quantiteACommander > 0 ? 'COMMANDER' : 'STOCK_SUFFISANT'
        };
      });

      // Filtrer seulement les ingrédients à commander
      const aCommander = suggestions.filter(s => s.recommandation === 'COMMANDER');
      const coutTotal = aCommander.reduce((sum, item) => sum + item.cout_estime, 0);

      return this.successResponse({
        periode_commande: joursCommande,
        ingredients_a_commander: aCommander,
        cout_total_estime: Math.round(coutTotal * 100) / 100,
        repartition_priorite: {
          haute: aCommander.filter(i => i.priorite === 'HAUTE').length,
          moyenne: aCommander.filter(i => i.priorite === 'MOYENNE').length,
          basse: aCommander.filter(i => i.priorite === 'BASSE').length
        },
        total_ingredients_analyses: suggestions.length
      });

    } catch (error) {
      logger.error('Erreur suggestions commandes optimales:', error);
      return this.rejectResponse('Erreur lors du calcul des suggestions de commande');
    }
  }

  /**
   * Réserver temporairement des ingrédients pour un produit
   */
  static async reserveIngredientsForProduct(produitId, quantite) {
    try {
      // Cette méthode peut être implémentée pour réserver temporairement
      // les ingrédients lors de l'ajout d'items à une commande
      // Pour l'instant, on fait juste une vérification
      return await this.checkIngredientAvailability(produitId, quantite);
    } catch (error) {
      logger.error('Erreur réservation ingrédients:', error);
      return this.rejectResponse('Erreur lors de la réservation des ingrédients');
    }
  }

  /**
   * Libérer des ingrédients réservés pour un produit
   */
  static async releaseIngredientsForProduct(produitId, quantite) {
    try {
      // Cette méthode peut être implémentée pour libérer des réservations temporaires
      // Pour l'instant, on retourne juste un succès
      return this.successResponse({
        message: 'Ingrédients libérés avec succès',
        produit_id: produitId,
        quantite: quantite
      });
    } catch (error) {
      logger.error('Erreur libération ingrédients:', error);
      return this.rejectResponse('Erreur lors de la libération des ingrédients');
    }
  }

  /**
   * Traiter la vente d'un produit avec déduction de stock
   */
  static async processProductSale(produitId, quantite, serviceId) {
    try {
      // Récupérer les ingrédients nécessaires pour ce produit
      const ingredientsQuery = await db.query(`
        SELECT
          pi.ingredient_id,
          pi.quantite_necessaire,
          i.nom as ingredient_nom,
          i.stock_actuel,
          i.unite_mesure
        FROM "ProduitsIngredients" pi
        JOIN "Ingredients" i ON pi.ingredient_id = i.ingredient_id
        WHERE pi.produit_id = $1 AND i.actif = true
      `, [produitId]);

      if (ingredientsQuery.rows.length === 0) {
        return this.successResponse({
          message: 'Aucun ingrédient trouvé pour ce produit',
          stock_updated: false
        });
      }

      await db.query('BEGIN');

      // Déduire le stock pour chaque ingrédient
      for (const ingredient of ingredientsQuery.rows) {
        const quantiteNecessaire = ingredient.quantite_necessaire * quantite;

        // Mettre à jour le stock
        await db.query(`
          UPDATE "Ingredients"
          SET stock_actuel = stock_actuel - $1,
              updated_at = NOW()
          WHERE ingredient_id = $2
        `, [quantiteNecessaire, ingredient.ingredient_id]);

        // Créer un mouvement de stock dans la table des ingrédients
        await db.query(`
          INSERT INTO "MouvementsStockIngredients" (
            ingredient_id, complexe_id, type_mouvement, quantite,
            quantite_avant, quantite_apres, prix_unitaire,
            reference_id, reference_type, notes, date_mouvement
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW())
        `, [
          ingredient.ingredient_id,
          1, // TODO: récupérer le complexe_id du service
          'CONSOMMATION',
          quantiteNecessaire,
          ingredient.stock_actuel,
          ingredient.stock_actuel - quantiteNecessaire,
          0, // prix_unitaire
          produitId,
          'VENTE_PRODUIT',
          `Service: ${serviceId}, Quantité vendue: ${quantite}`
        ]);
      }

      await db.query('COMMIT');

      return this.successResponse({
        message: 'Stock mis à jour avec succès',
        ingredients_updated: ingredientsQuery.rows.length,
        stock_updated: true
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur traitement vente produit:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du stock');
    }
  }

  /**
   * Restaurer le stock lors de l'annulation d'une vente
   */
  static async restoreStockForCancelledSale(produitId, quantite, serviceId) {
    try {
      // Récupérer les ingrédients du produit
      const ingredientsQuery = await db.query(`
        SELECT
          pi.ingredient_id,
          pi.quantite_necessaire,
          i.nom as ingredient_nom
        FROM "ProduitsIngredients" pi
        JOIN "Ingredients" i ON pi.ingredient_id = i.ingredient_id
        WHERE pi.produit_id = $1 AND i.actif = true
      `, [produitId]);

      if (ingredientsQuery.rows.length === 0) {
        return this.successResponse({
          message: 'Aucun ingrédient trouvé pour ce produit',
          stock_restored: false
        });
      }

      await db.query('BEGIN');

      // Restaurer le stock pour chaque ingrédient
      for (const ingredient of ingredientsQuery.rows) {
        const quantiteARestaurer = ingredient.quantite_necessaire * quantite;

        // Remettre le stock
        await db.query(`
          UPDATE "Ingredients"
          SET stock_actuel = stock_actuel + $1,
              updated_at = NOW()
          WHERE ingredient_id = $2
        `, [quantiteARestaurer, ingredient.ingredient_id]);

        // Créer un mouvement de stock de restauration
        await db.query(`
          INSERT INTO "MouvementsStockIngredients" (
            ingredient_id, complexe_id, type_mouvement, quantite,
            quantite_avant, quantite_apres, prix_unitaire,
            reference_id, reference_type, notes, date_mouvement
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW())
        `, [
          ingredient.ingredient_id,
          1, // TODO: récupérer le complexe_id du service
          'ENTREE',
          quantiteARestaurer,
          ingredient.stock_actuel - quantiteARestaurer, // stock avant restauration
          ingredient.stock_actuel, // stock après restauration
          0, // prix_unitaire
          produitId,
          'ANNULATION_VENTE',
          `Service: ${serviceId}, Quantité annulée: ${quantite}`
        ]);
      }

      await db.query('COMMIT');

      return this.successResponse({
        message: 'Stock restauré avec succès',
        ingredients_restored: ingredientsQuery.rows.length,
        stock_restored: true
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur restauration stock:', error);
      return this.rejectResponse('Erreur lors de la restauration du stock');
    }
  }

  /**
   * Vérifier la disponibilité d'un produit
   * @param {number} productId - ID du produit
   * @param {number} quantity - Quantité souhaitée
   * @returns {Promise<Object>} Statut de disponibilité
   */
  static async checkProductAvailability(productId, quantity) {
    try {
      // Vérifier que le produit existe et est actif
      const productResult = await db.query(`
        SELECT p.*, c.nom as categorie_nom
        FROM "Produits" p
        LEFT JOIN "Categories" c ON p.categorie_id = c.categorie_id
        WHERE p.produit_id = $1 AND p.actif = true
      `, [productId]);

      if (productResult.rows.length === 0) {
        return this.rejectResponse('Produit non trouvé ou inactif', 404);
      }

      const product = productResult.rows[0];

      // Vérifier la disponibilité des ingrédients
      const disponibiliteCheck = await this.checkIngredientAvailability(productId, quantity);

      if (!disponibiliteCheck.success) {
        return disponibiliteCheck;
      }

      // Construire la réponse
      return this.successResponse({
        produit_id: productId,
        nom: product.nom,
        categorie: product.categorie_nom,
        disponible: disponibiliteCheck.data.disponible,
        quantite_demandee: quantity,
        ingredients: disponibiliteCheck.data.ingredients_requis,
        alertes: disponibiliteCheck.data.alertes,
        message: disponibiliteCheck.data.message
      });

    } catch (error) {
      logger.error('Erreur vérification disponibilité produit:', error);
      return this.rejectResponse('Erreur lors de la vérification de disponibilité');
    }
  }

  /**
   * Obtenir le statut de stock d'un produit
   * @param {number} productId - ID du produit
   * @returns {Promise<Object>} Statut détaillé du stock
   */
  static async getProductStockStatus(productId) {
    try {
      // Récupérer les informations du produit
      const productResult = await db.query(`
        SELECT 
          p.*,
          c.nom as categorie_nom,
          COUNT(pi.ingredient_id) as total_ingredients
        FROM "Produits" p
        LEFT JOIN "Categories" c ON p.categorie_id = c.categorie_id
        LEFT JOIN "ProduitsIngredients" pi ON p.produit_id = pi.produit_id
        WHERE p.produit_id = $1
        GROUP BY p.produit_id, c.nom
      `, [productId]);

      if (productResult.rows.length === 0) {
        return this.rejectResponse('Produit non trouvé', 404);
      }

      const product = productResult.rows[0];

      // Récupérer le statut des ingrédients
      const ingredientsResult = await db.query(`
        SELECT
          i.ingredient_id,
          i.nom,
          i.stock_actuel,
          i.stock_minimal,
          i.unite_mesure,
          pi.quantite_necessaire,
          CASE 
            WHEN i.stock_actuel <= 0 THEN 'RUPTURE'
            WHEN i.stock_actuel <= i.stock_minimal THEN 'STOCK_FAIBLE'
            ELSE 'OK'
          END as statut_stock
        FROM "ProduitsIngredients" pi
        JOIN "Ingredients" i ON pi.ingredient_id = i.ingredient_id
        WHERE pi.produit_id = $1
        ORDER BY i.nom
      `, [productId]);

      const ingredients = ingredientsResult.rows;
      const statuts = {
        RUPTURE: ingredients.filter(i => i.statut_stock === 'RUPTURE').length,
        STOCK_FAIBLE: ingredients.filter(i => i.statut_stock === 'STOCK_FAIBLE').length,
        OK: ingredients.filter(i => i.statut_stock === 'OK').length
      };

      // Déterminer le statut global
      let statutGlobal = 'OK';
      if (statuts.RUPTURE > 0) {
        statutGlobal = 'RUPTURE';
      } else if (statuts.STOCK_FAIBLE > 0) {
        statutGlobal = 'STOCK_FAIBLE';
      }

      return this.successResponse({
        produit_id: productId,
        nom: product.nom,
        categorie: product.categorie_nom,
        statut_global: statutGlobal,
        total_ingredients: product.total_ingredients,
        repartition_statuts: statuts,
        ingredients: ingredients.map(i => ({
          ingredient_id: i.ingredient_id,
          nom: i.nom,
          stock_actuel: i.stock_actuel,
          stock_minimal: i.stock_minimal,
          unite_mesure: i.unite_mesure,
          quantite_necessaire: i.quantite_necessaire,
          statut: i.statut_stock
        }))
      });

    } catch (error) {
      logger.error('Erreur récupération statut stock produit:', error);
      return this.rejectResponse('Erreur lors de la récupération du statut de stock');
    }
  }

  /**
   * Valider la disponibilité des stocks pour une commande
   * @param {Array} orderItems - Liste des items de la commande
   * @returns {Promise<Object>} Résultat de la validation
   */
  static async validateOrderStock(orderItems) {
    try {
      const validations = [];
      const alertes = [];
      let globalValid = true;

      // Valider chaque item de la commande
      for (const item of orderItems) {
        const { produit_id, quantite } = item;

        // Vérifier la disponibilité
        const disponibiliteCheck = await this.checkProductAvailability(produit_id, quantite);

        const validation = {
          produit_id,
          quantite,
          disponible: disponibiliteCheck.success && disponibiliteCheck.data.disponible,
          message: disponibiliteCheck.success ? disponibiliteCheck.data.message : disponibiliteCheck.message
        };

        if (disponibiliteCheck.success) {
          // Ajouter les alertes de stock faible
          if (disponibiliteCheck.data.alertes.length > 0) {
            alertes.push(...disponibiliteCheck.data.alertes.map(a => ({
              ...a,
              produit_id,
              quantite_demandee: quantite
            })));
          }
        } else {
          globalValid = false;
        }

        validations.push(validation);
      }

      return this.successResponse({
        commande_valide: globalValid,
        validations,
        alertes,
        message: globalValid 
          ? 'Tous les produits sont disponibles'
          : 'Certains produits ne sont pas disponibles'
      });

    } catch (error) {
      logger.error('Erreur validation stocks commande:', error);
      return this.rejectResponse('Erreur lors de la validation des stocks');
    }
  }
}

module.exports = POSStockIntegration;
