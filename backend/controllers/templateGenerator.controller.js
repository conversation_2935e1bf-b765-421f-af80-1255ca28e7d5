const Controller = require('./Controller');
const TemplateGeneratorService = require('../services/templateGenerator.service');
const logger = require('../logger');
const path = require('path');

/**
 * Contrôleur pour la génération et téléchargement de templates Excel
 */
class TemplateGeneratorController extends Controller {

  /**
   * GET /api/templates/restaurant
   * Télécharger le template Excel pour menu restaurant (simple)
   */
  static async downloadRestaurantTemplate(request, response) {
    try {
      logger.info('Download restaurant template requested');

      const filename = 'restaurant-menu-template.xlsx';

      // Vérifier si le template existe, sinon le générer
      if (!TemplateGeneratorService.templateExists(filename)) {
        const workbook = TemplateGeneratorService.generateRestaurantTemplate();
        await TemplateGeneratorService.saveTemplate(workbook, filename);
      }

      const filepath = TemplateGeneratorService.getTemplatePath(filename);

      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      response.download(filepath, filename, (err) => {
        if (err) {
          logger.error('Error downloading restaurant template:', err);
          response.status(500).json({
            success: false,
            message: 'Erreur lors du téléchargement du template'
          });
        }
      });

    } catch (error) {
      logger.error('Error in downloadRestaurantTemplate:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du template restaurant'
      });
    }
  }

  /**
   * GET /api/templates/restaurant-with-ingredients
   * Télécharger le template Excel pour menu restaurant avec ingrédients
   */
  static async downloadRestaurantWithIngredientsTemplate(request, response) {
    try {
      logger.info('Download restaurant with ingredients template requested');

      const filename = 'restaurant-menu-with-ingredients-template.xlsx';

      // Vérifier si le template existe, sinon le générer
      if (!TemplateGeneratorService.templateExists(filename)) {
        const workbook = TemplateGeneratorService.generateRestaurantMenuWithIngredientsTemplate();
        await TemplateGeneratorService.saveTemplate(workbook, filename);
      }

      const filepath = TemplateGeneratorService.getTemplatePath(filename);

      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      response.download(filepath, filename, (err) => {
        if (err) {
          logger.error('Error downloading restaurant with ingredients template:', err);
          response.status(500).json({
            success: false,
            message: 'Erreur lors du téléchargement du template'
          });
        }
      });

    } catch (error) {
      logger.error('Error in downloadRestaurantWithIngredientsTemplate:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du template restaurant avec ingrédients'
      });
    }
  }

  /**
   * GET /api/templates/bar
   * Télécharger le template Excel pour carte bar (simple)
   */
  static async downloadBarTemplate(request, response) {
    try {
      logger.info('Download bar template requested');

      const filename = 'bar-menu-template.xlsx';

      // Vérifier si le template existe, sinon le générer
      if (!TemplateGeneratorService.templateExists(filename)) {
        const workbook = TemplateGeneratorService.generateBarTemplate();
        await TemplateGeneratorService.saveTemplate(workbook, filename);
      }

      const filepath = TemplateGeneratorService.getTemplatePath(filename);

      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      response.download(filepath, filename, (err) => {
        if (err) {
          logger.error('Error downloading bar template:', err);
          response.status(500).json({
            success: false,
            message: 'Erreur lors du téléchargement du template'
          });
        }
      });

    } catch (error) {
      logger.error('Error in downloadBarTemplate:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du template bar'
      });
    }
  }

  /**
   * GET /api/templates/bar-with-ingredients
   * Télécharger le template Excel pour carte bar avec ingrédients
   */
  static async downloadBarWithIngredientsTemplate(request, response) {
    try {
      logger.info('Download bar with ingredients template requested');

      const filename = 'bar-menu-with-ingredients-template.xlsx';

      // Vérifier si le template existe, sinon le générer
      if (!TemplateGeneratorService.templateExists(filename)) {
        const workbook = TemplateGeneratorService.generateBarMenuWithIngredientsTemplate();
        await TemplateGeneratorService.saveTemplate(workbook, filename);
      }

      const filepath = TemplateGeneratorService.getTemplatePath(filename);

      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      response.download(filepath, filename, (err) => {
        if (err) {
          logger.error('Error downloading bar with ingredients template:', err);
          response.status(500).json({
            success: false,
            message: 'Erreur lors du téléchargement du template'
          });
        }
      });

    } catch (error) {
      logger.error('Error in downloadBarWithIngredientsTemplate:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du template bar avec ingrédients'
      });
    }
  }

  /**
   * GET /api/templates/cuisine-ingredients
   * Télécharger le template Excel pour ingrédients cuisine
   */
  static async downloadCuisineIngredientTemplate(request, response) {
    try {
      logger.info('Download cuisine ingredient template requested');

      const filename = 'cuisine-ingredients-template.xlsx';
      
      // Vérifier si le template existe, sinon le générer
      if (!TemplateGeneratorService.templateExists(filename)) {
        const workbook = TemplateGeneratorService.generateCuisineIngredientTemplate();
        await TemplateGeneratorService.saveTemplate(workbook, filename);
      }

      const filepath = TemplateGeneratorService.getTemplatePath(filename);
      
      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      response.download(filepath, filename, (err) => {
        if (err) {
          logger.error('Error downloading cuisine ingredient template:', err);
          response.status(500).json({
            success: false,
            message: 'Erreur lors du téléchargement du template'
          });
        }
      });

    } catch (error) {
      logger.error('Error in downloadCuisineIngredientTemplate:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du template ingrédients'
      });
    }
  }

  /**
   * GET /api/templates/boisson-inventory
   * Télécharger le template Excel pour inventaire boissons
   */
  static async downloadBoissonInventoryTemplate(request, response) {
    try {
      logger.info('Download boisson inventory template requested');

      const filename = 'boisson-inventory-template.xlsx';
      
      // Vérifier si le template existe, sinon le générer
      if (!TemplateGeneratorService.templateExists(filename)) {
        const workbook = TemplateGeneratorService.generateBoissonInventoryTemplate();
        await TemplateGeneratorService.saveTemplate(workbook, filename);
      }

      const filepath = TemplateGeneratorService.getTemplatePath(filename);
      
      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      response.download(filepath, filename, (err) => {
        if (err) {
          logger.error('Error downloading boisson inventory template:', err);
          response.status(500).json({
            success: false,
            message: 'Erreur lors du téléchargement du template'
          });
        }
      });

    } catch (error) {
      logger.error('Error in downloadBoissonInventoryTemplate:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du template inventaire boissons'
      });
    }
  }

  /**
   * POST /api/templates/generate-all
   * Générer tous les templates Excel
   */
  static async generateAllTemplates(request, response) {
    try {
      logger.info('Generate all templates requested');

      const result = await TemplateGeneratorService.generateAllTemplates();

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error in generateAllTemplates:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération des templates'
      });
    }
  }

  /**
   * GET /api/templates/list
   * Lister tous les templates disponibles
   */
  static async listTemplates(request, response) {
    try {
      logger.info('List templates requested');

      const templates = [
        {
          name: 'Menu Restaurant',
          filename: 'restaurant-menu-template.xlsx',
          description: 'Template pour importer un menu de restaurant',
          endpoint: '/api/templates/restaurant'
        },
        {
          name: 'Carte Bar',
          filename: 'bar-menu-template.xlsx',
          description: 'Template pour importer une carte de bar',
          endpoint: '/api/templates/bar'
        },
        {
          name: 'Ingrédients Cuisine',
          filename: 'cuisine-ingredients-template.xlsx',
          description: 'Template pour importer les ingrédients de cuisine',
          endpoint: '/api/templates/cuisine-ingredients'
        },
        {
          name: 'Inventaire Boissons',
          filename: 'boisson-inventory-template.xlsx',
          description: 'Template pour importer l\'inventaire des boissons',
          endpoint: '/api/templates/boisson-inventory'
        }
      ];

      // Vérifier l'existence de chaque template
      const templatesWithStatus = templates.map(template => ({
        ...template,
        exists: TemplateGeneratorService.templateExists(template.filename),
        path: TemplateGeneratorService.getTemplatePath(template.filename)
      }));

      response.json({
        success: true,
        data: templatesWithStatus,
        message: 'Liste des templates récupérée avec succès'
      });

    } catch (error) {
      logger.error('Error in listTemplates:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération de la liste des templates'
      });
    }
  }
}

module.exports = TemplateGeneratorController;
