const db = require('../db');
const logger = require('../logger');

/**
 * Contr<PERSON>leur pour la gestion de l'inventaire des boissons
 */
class BoissonInventaireController {

  /**
   * GET /api/inventaire/boissons/:complexeId
   * R<PERSON>cup<PERSON>rer toutes les boissons d'un complexe
   */
  static async getBoissons(request, response) {
    try {
      const { complexeId } = request.params;
      const { categorie, actif, search } = request.query;

      logger.info('Get boissons', { complexeId, categorie, actif, search });

      let query = `
        SELECT 
          boisson_id,
          chaine_id,
          complexe_id,
          nom,
          description,
          categorie,
          type_conditionnement,
          volume_unitaire,
          prix_achat_unitaire,
          prix_vente_unitaire,
          fournisseur,
          degre_alcool,
          code_barre,
          stock_minimal,
          stock_maximal,
          emplacement_stockage,
          temperature_stockage,
          actif,
          created_at,
          updated_at
        FROM "InventaireBoissons"
        WHERE complexe_id = $1
      `;

      const params = [parseInt(complexeId)];
      let paramIndex = 2;

      if (categorie) {
        query += ` AND categorie = $${paramIndex}`;
        params.push(categorie);
        paramIndex++;
      }

      if (actif !== undefined) {
        query += ` AND actif = $${paramIndex}`;
        params.push(actif === 'true');
        paramIndex++;
      }

      if (search) {
        query += ` AND (nom ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
        params.push(`%${search}%`);
        paramIndex++;
      }

      query += ` ORDER BY nom ASC`;

      const result = await db.query(query, params);

      response.json({
        success: true,
        data: result.rows,
        message: 'Boissons récupérées avec succès'
      });

    } catch (error) {
      logger.error(`Error getting boissons for complexe ${request.params.complexeId}:`, error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des boissons'
      });
    }
  }

  /**
   * GET /api/inventaire/stock-boissons/:complexeId
   * Récupérer le stock des boissons avec statuts
   */
  static async getStockBoissons(request, response) {
    try {
      const { complexeId } = request.params;
      const { categorie, statutStock, emplacement } = request.query;

      logger.info('Get stock boissons', { complexeId, categorie, statutStock, emplacement });

      let query = `
        SELECT 
          sb.stock_id,
          sb.boisson_id,
          sb.complexe_id,
          sb.quantite_actuelle,
          sb.quantite_minimale,
          sb.quantite_maximale,
          sb.valeur_stock_actuel,
          sb.derniere_mise_a_jour,
          sb.created_at,
          sb.updated_at,
          ib.nom as nom_boisson,
          ib.categorie,
          ib.type_conditionnement,
          ib.volume_unitaire,
          ib.prix_achat_unitaire,
          ib.prix_vente_unitaire,
          ib.degre_alcool,
          ib.emplacement_stockage,
          ib.temperature_stockage,
          CASE 
            WHEN sb.quantite_actuelle <= sb.quantite_minimale THEN 'CRITIQUE'
            WHEN sb.quantite_actuelle <= (sb.quantite_minimale * 1.5) THEN 'FAIBLE'
            ELSE 'NORMAL'
          END as statut_stock,
          (sb.quantite_actuelle * ib.prix_vente_unitaire) as valeur_vente_potentielle
        FROM "StockBoissons" sb
        JOIN "InventaireBoissons" ib ON sb.boisson_id = ib.boisson_id
        WHERE sb.complexe_id = $1 AND ib.actif = true
      `;

      const params = [parseInt(complexeId)];
      let paramIndex = 2;

      if (categorie) {
        query += ` AND ib.categorie = $${paramIndex}`;
        params.push(categorie);
        paramIndex++;
      }

      if (emplacement) {
        query += ` AND ib.emplacement_stockage = $${paramIndex}`;
        params.push(emplacement);
        paramIndex++;
      }

      // Filtrer par statut après le calcul
      if (statutStock) {
        const statutCondition = {
          'CRITIQUE': 'sb.quantite_actuelle <= sb.quantite_minimale',
          'FAIBLE': 'sb.quantite_actuelle <= (sb.quantite_minimale * 1.5) AND sb.quantite_actuelle > sb.quantite_minimale',
          'NORMAL': 'sb.quantite_actuelle > (sb.quantite_minimale * 1.5)'
        };
        
        if (statutCondition[statutStock]) {
          query += ` AND ${statutCondition[statutStock]}`;
        }
      }

      query += ` ORDER BY ib.nom ASC`;

      const result = await db.query(query, params);

      response.json({
        success: true,
        data: result.rows,
        message: 'Stock des boissons récupéré avec succès'
      });

    } catch (error) {
      logger.error(`Error getting stock boissons for complexe ${request.params.complexeId}:`, error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du stock des boissons'
      });
    }
  }

  /**
   * POST /api/inventaire/mouvement-boisson
   * Enregistrer un mouvement de stock de boisson
   */
  static async enregistrerMouvementBoisson(request, response) {
    const client = await db.getClient();
    
    try {
      const { boisson_id, complexe_id, type_mouvement, quantite, prix_unitaire, notes } = request.body;

      logger.info('Enregistrer mouvement boisson', { boisson_id, complexe_id, type_mouvement, quantite });

      await client.query('BEGIN');

      // Récupérer le stock actuel
      const stockResult = await client.query(`
        SELECT quantite_actuelle, quantite_minimale, quantite_maximale
        FROM "StockBoissons"
        WHERE boisson_id = $1 AND complexe_id = $2
      `, [parseInt(boisson_id, 10), parseInt(complexe_id, 10)]);

      if (stockResult.rows.length === 0) {
        // Créer le stock s'il n'existe pas
        await client.query(`
          INSERT INTO "StockBoissons" (boisson_id, complexe_id, quantite_actuelle, quantite_minimale, quantite_maximale)
          SELECT $1, $2, 0, stock_minimal, stock_maximal
          FROM "InventaireBoissons"
          WHERE boisson_id = $1
        `, [parseInt(boisson_id, 10), parseInt(complexe_id, 10)]);
        
        const newStockResult = await client.query(`
          SELECT quantite_actuelle, quantite_minimale, quantite_maximale
          FROM "StockBoissons"
          WHERE boisson_id = $1 AND complexe_id = $2
        `, [parseInt(boisson_id, 10), parseInt(complexe_id, 10)]);
        
        stockResult.rows = newStockResult.rows;
      }

      const stockActuel = stockResult.rows[0];
      const quantiteAvant = stockActuel.quantite_actuelle;
      
      // Calculer la nouvelle quantité
      let quantiteApres = quantiteAvant;
      if (type_mouvement === 'ENTREE') {
        quantiteApres += quantite;
      } else if (['SORTIE', 'CONSOMMATION', 'PERTE'].includes(type_mouvement)) {
        quantiteApres -= quantite;
      } else if (type_mouvement === 'AJUSTEMENT') {
        quantiteApres = quantite; // Ajustement absolu
      }

      // Vérifier que la quantité ne devient pas négative
      if (quantiteApres < 0) {
        throw new Error('Stock insuffisant pour effectuer cette opération');
      }

      // Enregistrer le mouvement
      const mouvementResult = await client.query(`
        INSERT INTO "MouvementsStockBoissons" (
          boisson_id, complexe_id, type_mouvement, quantite,
          quantite_avant, quantite_apres, prix_unitaire,
          valeur_totale, notes, date_mouvement
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW())
        RETURNING *
      `, [
        parseInt(boisson_id, 10),
        parseInt(complexe_id, 10),
        type_mouvement,
        parseInt(quantite, 10),
        quantiteAvant,
        quantiteApres,
        parseFloat(prix_unitaire) || 0,
        (parseFloat(prix_unitaire) || 0) * parseInt(quantite, 10),
        notes
      ]);

      // Mettre à jour le stock
      await client.query(`
        UPDATE "StockBoissons"
        SET quantite_actuelle = $1::integer,
            valeur_stock_actuel = $1::integer * (
              SELECT prix_achat_unitaire
              FROM "InventaireBoissons"
              WHERE boisson_id = $2
            ),
            derniere_mise_a_jour = NOW(),
            updated_at = NOW()
        WHERE boisson_id = $2 AND complexe_id = $3
      `, [quantiteApres, parseInt(boisson_id, 10), parseInt(complexe_id, 10)]);

      await client.query('COMMIT');

      response.json({
        success: true,
        data: mouvementResult.rows[0],
        message: 'Mouvement de stock enregistré avec succès'
      });

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error enregistrer mouvement boisson:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'enregistrement du mouvement'
      });
    } finally {
      client.release();
    }
  }

  /**
   * GET /api/inventaire/mouvements-boissons/:complexeId
   * Récupérer l'historique des mouvements de stock des boissons
   */
  static async getHistoriqueMouvementsBoissons(request, response) {
    try {
      const { complexeId } = request.params;
      const { boissonId, typeMouvement, dateDebut, dateFin, limit = 20, offset = 0 } = request.query;

      logger.info('Get historique mouvements boissons', { complexeId, boissonId, typeMouvement });

      let query = `
        SELECT 
          msb.mouvement_id,
          msb.boisson_id,
          msb.complexe_id,
          msb.type_mouvement,
          msb.quantite,
          msb.quantite_avant,
          msb.quantite_apres,
          msb.prix_unitaire,
          msb.valeur_totale,
          msb.notes,
          msb.date_mouvement,
          msb.created_at,
          ib.nom as nom_boisson,
          ib.categorie,
          ib.type_conditionnement
        FROM "MouvementsStockBoissons" msb
        JOIN "InventaireBoissons" ib ON msb.boisson_id = ib.boisson_id
        WHERE msb.complexe_id = $1
      `;

      const params = [parseInt(complexeId)];
      let paramIndex = 2;

      if (boissonId) {
        query += ` AND msb.boisson_id = $${paramIndex}`;
        params.push(parseInt(boissonId));
        paramIndex++;
      }

      if (typeMouvement) {
        query += ` AND msb.type_mouvement = $${paramIndex}`;
        params.push(typeMouvement);
        paramIndex++;
      }

      if (dateDebut) {
        query += ` AND msb.date_mouvement >= $${paramIndex}`;
        params.push(dateDebut);
        paramIndex++;
      }

      if (dateFin) {
        query += ` AND msb.date_mouvement <= $${paramIndex}`;
        params.push(dateFin);
        paramIndex++;
      }

      // Compter le total
      const countQuery = query.replace(/SELECT[\s\S]*?FROM/, 'SELECT COUNT(*) as total FROM');
      const countResult = await db.query(countQuery, params);
      const total = countResult.rows.length > 0 ? parseInt(countResult.rows[0].total) : 0;

      // Ajouter pagination et tri
      query += ` ORDER BY msb.date_mouvement DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      params.push(parseInt(limit), parseInt(offset));

      const result = await db.query(query, params);

      response.json({
        success: true,
        data: {
          mouvements: result.rows,
          total: total
        },
        message: 'Historique des mouvements récupéré avec succès'
      });

    } catch (error) {
      logger.error(`Error getting historique mouvements boissons for complexe ${request.params.complexeId}:`, error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération de l\'historique'
      });
    }
  }

  /**
   * GET /api/inventaire/boissons/categories/:complexeId
   * Récupérer les catégories de boissons disponibles
   */
  static async getCategoriesBoissons(request, response) {
    try {
      const { complexeId } = request.params;

      const result = await db.query(`
        SELECT DISTINCT categorie
        FROM "InventaireBoissons"
        WHERE complexe_id = $1 AND actif = true
        ORDER BY categorie ASC
      `, [parseInt(complexeId)]);

      const categories = result.rows.map(row => row.categorie);

      response.json({
        success: true,
        data: categories,
        message: 'Catégories récupérées avec succès'
      });

    } catch (error) {
      logger.error(`Error getting categories boissons for complexe ${request.params.complexeId}:`, error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des catégories'
      });
    }
  }

  /**
   * GET /api/inventaire/boissons/emplacements/:complexeId
   * Récupérer les emplacements de stockage disponibles
   */
  static async getEmplacementsStockage(request, response) {
    try {
      const { complexeId } = request.params;

      const result = await db.query(`
        SELECT DISTINCT emplacement_stockage
        FROM "InventaireBoissons"
        WHERE complexe_id = $1 AND actif = true AND emplacement_stockage IS NOT NULL
        ORDER BY emplacement_stockage ASC
      `, [parseInt(complexeId)]);

      const emplacements = result.rows.map(row => row.emplacement_stockage);

      response.json({
        success: true,
        data: emplacements,
        message: 'Emplacements récupérés avec succès'
      });

    } catch (error) {
      logger.error(`Error getting emplacements stockage for complexe ${request.params.complexeId}:`, error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des emplacements'
      });
    }
  }
}

module.exports = BoissonInventaireController;
