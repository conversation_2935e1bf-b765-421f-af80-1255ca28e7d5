const express = require('express');
const router = express.Router();
const BoissonInventaireController = require('../controllers/boissonInventaire.controller');

// Import des middlewares spécialisés
const {
  cacheIngredients,
  cacheAnalytics,
  autoInvalidateCache,
  heavyResponseCache
} = require('../middleware/cache.middleware');
const {
  checkInventairePermissions,
  logInventaireAction
} = require('../middleware/inventaire.middleware');
const {
  ingredientLimiter,
  stockLimiter,
  analyticsLimiter
} = require('../middleware/rateLimiting.middleware');

/**
 * ==================== ROUTES DES BOISSONS ====================
 */

/**
 * GET /api/inventaire/boissons/:complexeId
 * Liste des boissons avec filtres
 */
router.get('/boissons/:complexeId',
  ingredientLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('ingredients', (req) => `boissons:${req.params.complexeId}:${JSON.stringify(req.query)}`, 600),
  BoissonInventaireController.getBoissons
);

/**
 * GET /api/inventaire/stock-boissons/:complexeId
 * État du stock des boissons par complexe
 */
router.get('/stock-boissons/:complexeId',
  stockLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `stock-boissons:${req.params.complexeId}:${JSON.stringify(req.query)}`, 600),
  BoissonInventaireController.getStockBoissons
);

/**
 * POST /api/inventaire/mouvement-boisson
 * Enregistrer un mouvement de stock de boisson
 */
router.post('/mouvement-boisson',
  stockLimiter,
  checkInventairePermissions(['manage_inventory']),
  autoInvalidateCache(['ingredients', 'analytics']),
  logInventaireAction('BOISSON_STOCK_MOVEMENT'),
  BoissonInventaireController.enregistrerMouvementBoisson
);

/**
 * GET /api/inventaire/mouvements-boissons/:complexeId
 * Historique des mouvements de stock des boissons
 */
router.get('/mouvements-boissons/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `mouvements-boissons:${req.params.complexeId}:${JSON.stringify(req.query)}`, 600),
  BoissonInventaireController.getHistoriqueMouvementsBoissons
);

/**
 * GET /api/inventaire/boissons/categories/:complexeId
 * Récupération des catégories de boissons disponibles
 */
router.get('/boissons/categories/:complexeId',
  ingredientLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('ingredients', (req) => `boisson-categories:${req.params.complexeId}`, 3600),
  BoissonInventaireController.getCategoriesBoissons
);

/**
 * GET /api/inventaire/boissons/emplacements/:complexeId
 * Récupération des emplacements de stockage disponibles
 */
router.get('/boissons/emplacements/:complexeId',
  ingredientLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('ingredients', (req) => `boisson-emplacements:${req.params.complexeId}`, 3600),
  BoissonInventaireController.getEmplacementsStockage
);

module.exports = router;
