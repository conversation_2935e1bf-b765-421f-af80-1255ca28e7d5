-- Migration pour ajouter la table des mouvements de stock des boissons
-- Date: 2024-01-15
-- Description: Création de la table MouvementsStockBoissons pour tracer les mouvements de stock des boissons

-- Table pour les mouvements de stock des boissons
CREATE TABLE IF NOT EXISTS "MouvementsStockBoissons" (
  "mouvement_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "boisson_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "type_mouvement" varchar NOT NULL CHECK (type_mouvement IN ('ENTREE', 'SORTIE', 'AJUSTEMENT', 'CONSOMMATION', 'PERTE')),
  "quantite" integer NOT NULL,
  "quantite_avant" integer NOT NULL DEFAULT 0,
  "quantite_apres" integer NOT NULL DEFAULT 0,
  "prix_unitaire" decimal DEFAULT 0,
  "valeur_totale" decimal DEFAULT 0,
  "reference_id" integer, -- ID de référence (commande, facture, etc.)
  "reference_type" varchar, -- Type de référence (COMMANDE, FACTURE, INVENTAIRE, etc.)
  "employe_id" integer, -- ID de l'employé qui a effectué le mouvement
  "notes" text, -- Notes sur le mouvement
  "date_mouvement" timestamp DEFAULT (now()),
  "created_at" timestamp DEFAULT (now()),
  FOREIGN KEY (boisson_id) REFERENCES "InventaireBoissons" (boisson_id),
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id)
);

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS "idx_mouvements_stock_boissons_boisson_id" ON "MouvementsStockBoissons" (boisson_id);
CREATE INDEX IF NOT EXISTS "idx_mouvements_stock_boissons_complexe_id" ON "MouvementsStockBoissons" (complexe_id);
CREATE INDEX IF NOT EXISTS "idx_mouvements_stock_boissons_date" ON "MouvementsStockBoissons" (date_mouvement);
CREATE INDEX IF NOT EXISTS "idx_mouvements_stock_boissons_type" ON "MouvementsStockBoissons" (type_mouvement);

-- Trigger pour mettre à jour automatiquement la valeur totale
CREATE OR REPLACE FUNCTION update_mouvement_boisson_valeur()
RETURNS TRIGGER AS $$
BEGIN
  NEW.valeur_totale = NEW.quantite * NEW.prix_unitaire;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_mouvement_boisson_valeur
  BEFORE INSERT OR UPDATE ON "MouvementsStockBoissons"
  FOR EACH ROW
  EXECUTE FUNCTION update_mouvement_boisson_valeur();

-- Vue pour l'historique des mouvements avec informations détaillées
CREATE OR REPLACE VIEW "VueMouvementsStockBoissons" AS
SELECT
    msb.mouvement_id,
    msb.boisson_id,
    msb.complexe_id,
    msb.type_mouvement,
    msb.quantite,
    msb.quantite_avant,
    msb.quantite_apres,
    msb.prix_unitaire,
    msb.valeur_totale,
    msb.reference_id,
    msb.reference_type,
    msb.employe_id,
    msb.notes,
    msb.date_mouvement,
    msb.created_at,
    ib.nom as nom_boisson,
    ib.categorie,
    ib.type_conditionnement,
    ib.volume_unitaire,
    ib.degre_alcool,
    ch.nom as nom_complexe
FROM "MouvementsStockBoissons" msb
JOIN "InventaireBoissons" ib ON msb.boisson_id = ib.boisson_id
JOIN "ComplexesHoteliers" ch ON msb.complexe_id = ch.complexe_id;

-- Commentaires pour la documentation
COMMENT ON TABLE "MouvementsStockBoissons" IS 'Table pour tracer tous les mouvements de stock des boissons (entrées, sorties, ajustements, consommations, pertes)';
COMMENT ON COLUMN "MouvementsStockBoissons"."type_mouvement" IS 'Type de mouvement: ENTREE (réception), SORTIE (vente), AJUSTEMENT (correction), CONSOMMATION (utilisation), PERTE (casse/péremption)';
COMMENT ON COLUMN "MouvementsStockBoissons"."quantite" IS 'Quantité du mouvement (positive pour entrée, négative pour sortie)';
COMMENT ON COLUMN "MouvementsStockBoissons"."quantite_avant" IS 'Stock avant le mouvement';
COMMENT ON COLUMN "MouvementsStockBoissons"."quantite_apres" IS 'Stock après le mouvement';
COMMENT ON COLUMN "MouvementsStockBoissons"."reference_id" IS 'ID de référence vers une autre entité (commande, facture, etc.)';
COMMENT ON COLUMN "MouvementsStockBoissons"."reference_type" IS 'Type de la référence (COMMANDE, FACTURE, INVENTAIRE, MANUEL, etc.)';

-- Données de test (optionnel)
-- INSERT INTO "MouvementsStockBoissons" (boisson_id, complexe_id, type_mouvement, quantite, quantite_avant, quantite_apres, prix_unitaire, notes)
-- VALUES 
-- (1, 1, 'ENTREE', 50, 0, 50, 2500, 'Stock initial - Heineken'),
-- (2, 1, 'ENTREE', 30, 0, 30, 15000, 'Stock initial - Château Margaux'),
-- (1, 1, 'SORTIE', 5, 50, 45, 2500, 'Vente soirée du 15/01');

COMMIT;
