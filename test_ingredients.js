const XLSX = require('./backend/node_modules/xlsx');
const fs = require('fs');

// Créer des données de test pour les ingrédients
const testData = [
  {
    nom_ingredient: "Tomates fraîches",
    description: "Tomates de saison",
    categorie: "Légume",
    unite_mesure: "kg",
    prix_unitaire: 1000,
    stock_initial: 25,
    stock_minimal: 5,
    stock_maximal: 50,
    conservation: "Frais",
    duree_conservation_jours: 7,
    actif: true
  },
  {
    nom_ingredient: "Farine de blé",
    description: "Farine T55 pour pâtisserie",
    categorie: "Céréale",
    unite_mesure: "kg",
    prix_unitaire: 1000,
    stock_initial: 50,
    stock_minimal: 10,
    stock_maximal: 100,
    allergenes: "Gluten",
    conservation: "Sec",
    duree_conservation_jours: 365,
    actif: true
  },
  {
    nom_ingredient: "Bœuf (entrecôte)",
    description: "Viande de bœuf première qualité",
    categorie: "Viande",
    unite_mesure: "kg",
    prix_unitaire: 1000,
    stock_initial: 15,
    stock_minimal: 2,
    stock_maximal: 30,
    conservation: "Frais",
    duree_conservation_jours: 3,
    actif: true
  },
  {
    nom_ingredient: "Huile d'olive",
    description: "Huile d'olive extra vierge",
    categorie: "Matière grasse",
    unite_mesure: "L",
    prix_unitaire: 1000,
    stock_initial: 10,
    stock_minimal: 3,
    stock_maximal: 20,
    conservation: "Sec",
    duree_conservation_jours: 730,
    actif: true
  }
];

// Créer un workbook Excel
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.json_to_sheet(testData);

// Ajouter la feuille au workbook
XLSX.utils.book_append_sheet(workbook, worksheet, 'Ingredients');

// Sauvegarder le fichier
XLSX.writeFile(workbook, 'test_ingredients.xlsx');

console.log('Fichier test_ingredients.xlsx créé avec succès');
console.log('Données de test:', testData);
