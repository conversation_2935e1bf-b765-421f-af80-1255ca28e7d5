import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { ArrowLeft, CheckCircle, Clock, ChefHat, Wine } from 'lucide-react';
import { authService } from '../services/auth.service';
import { serviceComplexeService } from '../services/service.service';
import { MenuUploader, IngredientUploader } from '../components/upload';
import type { ServiceComplexe } from '../services/service.service';

interface SetupStepProps {
  title: string;
  description: string;
  children: React.ReactNode;
  completed?: boolean;
  current?: boolean;
}

const SetupStep: React.FC<SetupStepProps> = ({ 
  title, 
  description, 
  children, 
  completed = false, 
  current = false 
}) => {
  return (
    <div className={`border rounded-lg p-6 ${
      current ? 'border-blue-300 bg-blue-50' : 
      completed ? 'border-green-300 bg-green-50' : 
      'border-gray-200 bg-white'
    }`}>
      <div className="flex items-center mb-4">
        {completed ? (
          <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
        ) : current ? (
          <Clock className="h-6 w-6 text-blue-500 mr-3" />
        ) : (
          <div className="h-6 w-6 rounded-full border-2 border-gray-300 mr-3" />
        )}
        <div>
          <h3 className={`text-lg font-medium ${
            current ? 'text-blue-900' : 
            completed ? 'text-green-900' : 
            'text-gray-900'
          }`}>
            {title}
          </h3>
          <p className={`text-sm ${
            current ? 'text-blue-700' : 
            completed ? 'text-green-700' : 
            'text-gray-600'
          }`}>
            {description}
          </p>
        </div>
      </div>
      {children}
    </div>
  );
};

interface ServiceTypeSelectorProps {
  value: 'Restaurant' | 'Bar';
  onChange: (value: 'Restaurant' | 'Bar') => void;
}

const ServiceTypeSelector: React.FC<ServiceTypeSelectorProps> = ({ value, onChange }) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Sélectionnez le type de service
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <button
          onClick={() => onChange('Restaurant')}
          className={`p-6 rounded-lg border-2 transition-all duration-200 ${
            value === 'Restaurant'
              ? 'border-blue-500 bg-blue-50 text-blue-900'
              : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
          }`}
        >
          <ChefHat className="h-12 w-12 mx-auto mb-3" />
          <h3 className="text-lg font-medium mb-2">Restaurant</h3>
          <p className="text-sm">
            Import d'ingrédients cuisine et menu restaurant
          </p>
        </button>
        <button
          onClick={() => onChange('Bar')}
          className={`p-6 rounded-lg border-2 transition-all duration-200 ${
            value === 'Bar'
              ? 'border-blue-500 bg-blue-50 text-blue-900'
              : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
          }`}
        >
          <Wine className="h-12 w-12 mx-auto mb-3" />
          <h3 className="text-lg font-medium mb-2">Bar</h3>
          <p className="text-sm">
            Import d'inventaire boissons et carte bar
          </p>
        </button>
      </div>
    </div>
  );
};

export const MenuSetup: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const complexeId = authService.getComplexeId();
  
  const [serviceType, setServiceType] = useState<'Restaurant' | 'Bar'>('Restaurant');
  const [selectedService, setSelectedService] = useState<ServiceComplexe | null>(null);
  const [services, setServices] = useState<ServiceComplexe[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState<'select-service' | 'ingredients' | 'menu' | 'complete'>('select-service');
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  // Récupération des services au chargement
  useEffect(() => {
    const loadServices = async () => {
      if (!complexeId) return;
      
      try {
        const allServices = await serviceComplexeService.getAllServices();
        const filteredServices = allServices.filter(s => 
          s.type_service === 'Restaurant' || s.type_service === 'Bar'
        );
        setServices(filteredServices);
        
        // Auto-sélection si service spécifié dans l'URL
        const serviceIdParam = searchParams.get('serviceId');
        if (serviceIdParam) {
          const service = filteredServices.find(s => s.service_id === parseInt(serviceIdParam));
          if (service) {
            setSelectedService(service);
            setServiceType(service.type_service as 'Restaurant' | 'Bar');
            setCurrentStep('ingredients');
          }
        }
      } catch (error) {
        console.error('Erreur lors du chargement des services:', error);
        toast.error('Erreur lors du chargement des services');
      } finally {
        setLoading(false);
      }
    };

    loadServices();
  }, [complexeId, searchParams]);

  const handleServiceSelect = (service: ServiceComplexe) => {
    setSelectedService(service);
    setServiceType(service.type_service as 'Restaurant' | 'Bar');
    setCurrentStep('ingredients');
  };

  const handleIngredientUploadComplete = () => {
    setCompletedSteps(prev => new Set([...prev, 'ingredients']));
    setCurrentStep('menu');
    toast.success('Ingrédients importés avec succès !');
  };

  const handleMenuUploadComplete = () => {
    setCompletedSteps(prev => new Set([...prev, 'menu']));
    setCurrentStep('complete');
    toast.success('Menu importé avec succès !');
  };

  const handleReset = () => {
    setCurrentStep('select-service');
    setSelectedService(null);
    setCompletedSteps(new Set());
  };

  if (!complexeId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            Vous devez être connecté à un complexe pour accéder à cette page.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Configuration Menu Automatique
              </h1>
              <p className="text-lg text-gray-600">
                Configurez votre menu avec gestion automatique des stocks en 3 étapes
              </p>
            </div>
            <button
              onClick={() => navigate('/services')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Retour aux Services
            </button>
          </div>
        </div>

        <div className="space-y-8">
          {/* Étape 1: Sélection du service */}
          {currentStep === 'select-service' && (
            <>
              <ServiceTypeSelector
                value={serviceType}
                onChange={setServiceType}
              />
              
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Sélectionnez un service {serviceType}
                </h2>
                
                {services.filter(s => s.type_service === serviceType).length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-600 mb-4">
                      Aucun service {serviceType} trouvé.
                    </p>
                    <button
                      onClick={() => navigate('/services')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      Créer un service {serviceType}
                    </button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {services
                      .filter(s => s.type_service === serviceType)
                      .map((service) => (
                        <button
                          key={service.service_id}
                          onClick={() => handleServiceSelect(service)}
                          className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 text-left transition-colors"
                        >
                          <h3 className="font-medium text-gray-900">{service.nom}</h3>
                          {service.description && (
                            <p className="text-sm text-gray-600 mt-1">{service.description}</p>
                          )}
                          <p className="text-xs text-gray-500 mt-2">
                            {service.emplacement}
                          </p>
                        </button>
                      ))}
                  </div>
                )}
              </div>
            </>
          )}

          {/* Étapes d'import */}
          {selectedService && currentStep !== 'select-service' && (
            <>
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h2 className="text-lg font-medium text-gray-900">
                  Configuration pour : {selectedService.nom}
                </h2>
                <p className="text-sm text-gray-600">
                  Type: {selectedService.type_service}
                </p>
              </div>

              {/* Étape 2: Import ingrédients/inventaire */}
              <SetupStep
                title={`1. Importer ${serviceType === 'Restaurant' ? 'les Ingrédients Cuisine' : 'l\'Inventaire Boissons'}`}
                description={serviceType === 'Restaurant'
                  ? 'Uploadez votre liste d\'ingrédients avec stocks initiaux et minimaux pour la gestion automatique'
                  : 'Uploadez votre inventaire de boissons avec stocks initiaux et minimaux pour la gestion automatique'
                }
                completed={completedSteps.has('ingredients')}
                current={currentStep === 'ingredients'}
              >
                {currentStep === 'ingredients' && (
                  <IngredientUploader
                    complexeId={complexeId}
                    type={serviceType === 'Restaurant' ? 'cuisine' : 'boissons'}
                    onUploadComplete={handleIngredientUploadComplete}
                    onError={(error) => toast.error(`Erreur: ${error}`)}
                  />
                )}
              </SetupStep>

              {/* Étape 3: Import menu/carte */}
              <SetupStep
                title={`2. Importer ${serviceType === 'Restaurant' ? 'le Menu' : 'la Carte'}`}
                description={serviceType === 'Restaurant'
                  ? 'Uploadez votre menu avec les ingrédients utilisés - les liens seront créés automatiquement'
                  : 'Uploadez votre carte avec les ingrédients/composants - les liens seront créés automatiquement'
                }
                completed={completedSteps.has('menu')}
                current={currentStep === 'menu'}
              >
                {currentStep === 'menu' && (
                  <MenuUploader
                    serviceId={selectedService.service_id}
                    serviceType={serviceType}
                    onUploadComplete={handleMenuUploadComplete}
                    onError={(error) => toast.error(`Erreur: ${error}`)}
                  />
                )}
              </SetupStep>

              {/* Étape 4: Terminé */}
              {currentStep === 'complete' && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                  <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-green-900 mb-2">
                    Configuration terminée !
                  </h2>
                  <p className="text-green-700 mb-6">
                    Votre {serviceType === 'Restaurant' ? 'menu' : 'carte'} est maintenant opérationnel avec gestion automatique des stocks !
                  </p>
                  <div className="bg-white border border-green-200 rounded-lg p-4 mb-6 text-left">
                    <h3 className="font-semibold text-green-800 mb-2">✅ Système configuré :</h3>
                    <ul className="text-sm text-green-700 space-y-1">
                      <li>• Stocks initialisés automatiquement</li>
                      <li>• Liens produits-ingrédients créés automatiquement</li>
                      <li>• Déduction automatique lors des ventes POS</li>
                      <li>• Alertes de stock faible activées</li>
                    </ul>
                  </div>
                  <div className="flex justify-center space-x-4">
                    <button
                      onClick={() => navigate('/services')}
                      className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
                    >
                      Voir les Services
                    </button>
                    <button
                      onClick={handleReset}
                      className="px-6 py-3 border border-green-600 text-green-600 rounded-lg hover:bg-green-50 font-medium"
                    >
                      Configurer un autre service
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
