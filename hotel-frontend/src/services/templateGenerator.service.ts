import api from './api.config';
import { authService } from './auth.service';

// Types pour les templates
export interface TemplateInfo {
  name: string;
  description: string;
  type: 'restaurant' | 'bar' | 'cuisine' | 'boissons';
  requiredColumns: TemplateColumn[];
  optionalColumns: TemplateColumn[];
  exampleData: any[];
  validationRules: ValidationRule[];
}

export interface TemplateColumn {
  name: string;
  type: 'string' | 'number' | 'decimal' | 'boolean' | 'date';
  required: boolean;
  description: string;
  example?: any;
  validValues?: string[];
}

export interface ValidationRule {
  field: string;
  type: 'required' | 'min' | 'max' | 'pattern' | 'enum';
  value?: any;
  message: string;
}

/**
 * Service pour la génération et le téléchargement des templates Excel
 */
class TemplateGeneratorService {
  private baseURL = 'template-generator';

  /**
   * Vérification de l'authentification
   */
  private checkAuth(): void {
    if (!authService.isAuthenticated()) {
      throw new Error('Utilisateur non authentifié');
    }
  }

  /**
   * Téléchargement du template Excel pour menu restaurant avec ingrédients
   */
  async downloadRestaurantTemplate(): Promise<Blob> {
    this.checkAuth();

    try {
      const response = await api.get(`${this.baseURL}/restaurant-with-ingredients`, {
        responseType: 'blob',
        timeout: 30000,
      });

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Erreur lors du téléchargement du template restaurant'
      );
    }
  }

  /**
   * Téléchargement du template Excel pour carte bar avec ingrédients
   */
  async downloadBarTemplate(): Promise<Blob> {
    this.checkAuth();

    try {
      const response = await api.get(`${this.baseURL}/bar-with-ingredients`, {
        responseType: 'blob',
        timeout: 30000,
      });

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Erreur lors du téléchargement du template bar'
      );
    }
  }

  /**
   * Téléchargement du template Excel pour ingrédients cuisine
   */
  async downloadCuisineTemplate(): Promise<Blob> {
    this.checkAuth();

    try {
      const response = await api.get(`${this.baseURL}/cuisine-ingredients`, {
        responseType: 'blob',
        timeout: 30000,
      });

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Erreur lors du téléchargement du template ingrédients cuisine'
      );
    }
  }

  /**
   * Téléchargement du template Excel pour inventaire boissons
   */
  async downloadBoissonTemplate(): Promise<Blob> {
    this.checkAuth();

    try {
      const response = await api.get(`${this.baseURL}/boisson-inventory`, {
        responseType: 'blob',
        timeout: 30000,
      });

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Erreur lors du téléchargement du template inventaire boissons'
      );
    }
  }

  /**
   * Récupération des informations sur un template spécifique
   */
  async getTemplateInfo(type: 'restaurant' | 'bar' | 'cuisine' | 'boissons'): Promise<TemplateInfo> {
    this.checkAuth();

    try {
      // Mapper les types vers les endpoints backend
      const endpointMap = {
        'restaurant': 'restaurant',
        'bar': 'bar',
        'cuisine': 'cuisine-ingredients',
        'boissons': 'boisson-inventory'
      };

      const response = await api.get<{
        success: boolean;
        message: string;
        data: TemplateInfo;
      }>(`${this.baseURL}/info/${endpointMap[type]}`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération des informations du template');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Erreur lors de la récupération des informations du template'
      );
    }
  }

  /**
   * Récupération de la liste de tous les templates disponibles
   */
  async getAllTemplatesInfo(): Promise<TemplateInfo[]> {
    this.checkAuth();

    try {
      const response = await api.get<{
        success: boolean;
        message: string;
        data: TemplateInfo[];
      }>(`${this.baseURL}/info`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération des templates');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération des templates'
      );
    }
  }

  /**
   * Utilitaire pour télécharger un fichier blob
   */
  downloadFile(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  /**
   * Téléchargement direct avec nom de fichier automatique
   */
  async downloadTemplateWithFilename(type: 'restaurant' | 'bar' | 'cuisine' | 'boissons'): Promise<void> {
    let blob: Blob;
    let filename: string;

    switch (type) {
      case 'restaurant':
        blob = await this.downloadRestaurantTemplate();
        filename = 'template-menu-restaurant-avec-ingredients.xlsx';
        break;
      case 'bar':
        blob = await this.downloadBarTemplate();
        filename = 'template-carte-bar-avec-ingredients.xlsx';
        break;
      case 'cuisine':
        blob = await this.downloadCuisineTemplate();
        filename = 'template-ingredients-cuisine.xlsx';
        break;
      case 'boissons':
        blob = await this.downloadBoissonTemplate();
        filename = 'template-inventaire-boissons.xlsx';
        break;
      default:
        throw new Error('Type de template non supporté');
    }

    this.downloadFile(blob, filename);
  }

  /**
   * Validation du format d'un fichier avant upload
   */
  validateFileFormat(file: File): { isValid: boolean; error?: string } {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
    ];

    const allowedExtensions = ['.xlsx', '.xls'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      return {
        isValid: false,
        error: 'Format de fichier non supporté. Veuillez utiliser un fichier Excel (.xlsx ou .xls)'
      };
    }

    // Vérification de la taille (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'Le fichier est trop volumineux. Taille maximum autorisée : 10MB'
      };
    }

    return { isValid: true };
  }
}

export const templateGeneratorService = new TemplateGeneratorService();
export default templateGeneratorService;
