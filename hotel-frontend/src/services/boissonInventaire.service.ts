import api from './api.config';
import { authService } from './auth.service';

// Types pour la gestion des boissons
export interface Boisson {
  boisson_id: number;
  chaine_id: number;
  complexe_id: number;
  nom: string;
  description?: string;
  categorie: string;
  type_conditionnement: string;
  volume_unitaire: number;
  prix_achat_unitaire: number;
  prix_vente_unitaire: number;
  fournisseur?: string;
  degre_alcool: number;
  code_barre?: string;
  stock_minimal: number;
  stock_maximal: number;
  emplacement_stockage?: string;
  temperature_stockage?: string;
  actif: boolean;
  created_at: string;
  updated_at?: string;
}

export interface StockBoisson {
  stock_id: number;
  boisson_id: number;
  complexe_id: number;
  quantite_actuelle: number;
  quantite_minimale: number;
  quantite_maximale: number;
  valeur_stock_actuel: number;
  derniere_mise_a_jour: string;
  created_at: string;
  updated_at?: string;
  // Informations de la boisson
  nom_boisson: string;
  categorie: string;
  type_conditionnement: string;
  volume_unitaire: number;
  prix_achat_unitaire: number;
  prix_vente_unitaire: number;
  degre_alcool: number;
  emplacement_stockage?: string;
  temperature_stockage?: string;
  statut_stock: 'NORMAL' | 'FAIBLE' | 'CRITIQUE';
  valeur_vente_potentielle: number;
}

export interface MouvementStockBoisson {
  mouvement_id: number;
  boisson_id: number;
  complexe_id: number;
  type_mouvement: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT' | 'CONSOMMATION' | 'PERTE';
  quantite: number;
  quantite_avant: number;
  quantite_apres: number;
  prix_unitaire: number;
  valeur_totale: number;
  reference_id?: number;
  reference_type?: string;
  employe_id?: number;
  notes?: string;
  date_mouvement: string;
  created_at: string;
  // Informations de la boisson
  nom_boisson: string;
  categorie: string;
  type_conditionnement: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

/**
 * Service pour la gestion de l'inventaire des boissons
 * Utilise les vraies routes du backend existant
 */
class BoissonInventaireService {
  private baseURL = 'inventaire';
  private importURL = 'ingredient-import';

  /**
   * Vérification de l'authentification
   */
  private checkAuth(): void {
    if (!authService.isAuthenticated()) {
      throw new Error('Utilisateur non authentifié');
    }
  }

  /**
   * ==================== GESTION DES BOISSONS ====================
   */

  /**
   * Récupérer toutes les boissons d'un complexe
   */
  async getBoissons(
    complexeId: number,
    filters?: {
      categorie?: string;
      actif?: boolean;
      search?: string;
    }
  ): Promise<Boisson[]> {
    this.checkAuth();

    try {
      const params = new URLSearchParams();
      if (filters?.categorie) params.append('categorie', filters.categorie);
      if (filters?.actif !== undefined) params.append('actif', filters.actif.toString());
      if (filters?.search) params.append('search', filters.search);

      const response = await api.get<ApiResponse<Boisson[]>>(
        `${this.baseURL}/boissons/${complexeId}?${params.toString()}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération des boissons');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Erreur lors de la récupération des boissons'
      );
    }
  }

  /**
   * Récupérer le stock des boissons
   */
  async getStockBoissons(
    complexeId: number,
    filters?: {
      categorie?: string;
      statutStock?: 'NORMAL' | 'FAIBLE' | 'CRITIQUE';
      emplacement?: string;
    }
  ): Promise<StockBoisson[]> {
    this.checkAuth();

    try {
      const params = new URLSearchParams();
      if (filters?.categorie) params.append('categorie', filters.categorie);
      if (filters?.statutStock) params.append('statut_stock', filters.statutStock);
      if (filters?.emplacement) params.append('emplacement', filters.emplacement);

      const response = await api.get<ApiResponse<StockBoisson[]>>(
        `${this.baseURL}/stock-boissons/${complexeId}?${params.toString()}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération du stock des boissons');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération du stock des boissons'
      );
    }
  }

  /**
   * Récupérer l'historique des mouvements de stock des boissons
   */
  async getHistoriqueMouvementsBoissons(
    complexeId: number,
    filters?: {
      boissonId?: number;
      typeMouvement?: string;
      dateDebut?: string;
      dateFin?: string;
      limit?: number;
      offset?: number;
    }
  ): Promise<{ mouvements: MouvementStockBoisson[]; total: number }> {
    this.checkAuth();

    try {
      const params = new URLSearchParams();
      if (filters?.boissonId) params.append('boisson_id', filters.boissonId.toString());
      if (filters?.typeMouvement) params.append('type_mouvement', filters.typeMouvement);
      if (filters?.dateDebut) params.append('date_debut', filters.dateDebut);
      if (filters?.dateFin) params.append('date_fin', filters.dateFin);
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.offset) params.append('offset', filters.offset.toString());

      const response = await api.get<ApiResponse<{ mouvements: MouvementStockBoisson[]; total: number }>>(
        `${this.baseURL}/mouvements-boissons/${complexeId}?${params.toString()}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération de l\'historique');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération de l\'historique des mouvements'
      );
    }
  }

  /**
   * Enregistrer un mouvement de stock de boisson
   */
  async enregistrerMouvementBoisson(
    boissonId: number,
    complexeId: number,
    typeMouvement: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT' | 'PERTE',
    quantite: number,
    options?: {
      prixUnitaire?: number;
      notes?: string;
    }
  ): Promise<MouvementStockBoisson> {
    this.checkAuth();

    try {
      const response = await api.post<ApiResponse<MouvementStockBoisson>>(
        `${this.baseURL}/mouvement-boisson`,
        {
          boisson_id: boissonId,
          complexe_id: complexeId,
          type_mouvement: typeMouvement,
          quantite,
          prix_unitaire: options?.prixUnitaire,
          notes: options?.notes
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'enregistrement du mouvement');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de l\'enregistrement du mouvement'
      );
    }
  }

  /**
   * Récupérer les catégories de boissons disponibles
   */
  async getCategoriesBoissons(complexeId: number): Promise<string[]> {
    this.checkAuth();

    try {
      const response = await api.get<ApiResponse<string[]>>(
        `${this.baseURL}/boissons/categories/${complexeId}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération des catégories');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération des catégories'
      );
    }
  }

  /**
   * Récupérer les emplacements de stockage disponibles
   */
  async getEmplacementsStockage(complexeId: number): Promise<string[]> {
    this.checkAuth();

    try {
      const response = await api.get<ApiResponse<string[]>>(
        `${this.baseURL}/boissons/emplacements/${complexeId}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération des emplacements');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération des emplacements'
      );
    }
  }
}

export const boissonInventaireService = new BoissonInventaireService();
export default boissonInventaireService;
