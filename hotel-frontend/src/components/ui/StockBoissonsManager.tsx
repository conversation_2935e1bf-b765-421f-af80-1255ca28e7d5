import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import {
  Wine,
  Plus,
  Search,
  Filter,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Minus,
  Thermometer,
  MapPin
} from 'lucide-react';
import boissonInventaireService, { type StockBoisson } from '../../services/boissonInventaire.service';

interface StockBoissonsManagerProps {
  complexeId: number;
}

export const StockBoissonsManager: React.FC<StockBoissonsManagerProps> = ({
  complexeId
}) => {
  const [stockBoissons, setStockBoissons] = useState<StockBoisson[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategorie, setSelectedCategorie] = useState<string>('');
  const [selectedStatut, setSelectedStatut] = useState<string>('');
  const [selectedEmplacement, setSelectedEmplacement] = useState<string>('');
  const [categories, setCategories] = useState<string[]>([]);
  const [emplacements, setEmplacements] = useState<string[]>([]);
  const [showMouvementModal, setShowMouvementModal] = useState(false);
  const [selectedBoisson, setSelectedBoisson] = useState<StockBoisson | null>(null);
  const [mouvementData, setMouvementData] = useState({
    type: 'ENTREE' as 'ENTREE' | 'SORTIE' | 'AJUSTEMENT' | 'PERTE',
    quantite: 0,
    notes: ''
  });

  useEffect(() => {
    loadStockBoissons();
    loadCategories();
    loadEmplacements();
  }, [complexeId, selectedCategorie, selectedStatut, selectedEmplacement]);

  const loadStockBoissons = async () => {
    try {
      setLoading(true);
      const response = await boissonInventaireService.getStockBoissons(complexeId, {
        categorie: selectedCategorie || undefined,
        statutStock: selectedStatut as any || undefined,
        emplacement: selectedEmplacement || undefined
      });
      
      setStockBoissons(response);
    } catch (error: any) {
      toast.error(error.message || 'Erreur lors du chargement des stocks de boissons');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const categories = await boissonInventaireService.getCategoriesBoissons(complexeId);
      setCategories(categories);
    } catch (error: any) {
      console.error('Erreur lors du chargement des catégories:', error);
    }
  };

  const loadEmplacements = async () => {
    try {
      const emplacements = await boissonInventaireService.getEmplacementsStockage(complexeId);
      setEmplacements(emplacements);
    } catch (error: any) {
      console.error('Erreur lors du chargement des emplacements:', error);
    }
  };

  const handleMouvement = async () => {
    if (!selectedBoisson || mouvementData.quantite <= 0) {
      toast.error('Veuillez saisir une quantité valide');
      return;
    }

    try {
      await boissonInventaireService.enregistrerMouvementBoisson(
        selectedBoisson.boisson_id,
        complexeId,
        mouvementData.type,
        mouvementData.quantite,
        {
          notes: mouvementData.notes
        }
      );

      toast.success('Mouvement enregistré avec succès');
      setShowMouvementModal(false);
      setSelectedBoisson(null);
      setMouvementData({ type: 'ENTREE', quantite: 0, notes: '' });
      loadStockBoissons();
    } catch (error: any) {
      toast.error(error.message || 'Erreur lors de l\'enregistrement du mouvement');
    }
  };

  const getStatutColor = (statut: string) => {
    switch (statut) {
      case 'CRITIQUE':
        return 'text-red-600 bg-red-100';
      case 'FAIBLE':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-green-600 bg-green-100';
    }
  };

  const getStatutIcon = (statut: string) => {
    switch (statut) {
      case 'CRITIQUE':
        return <AlertTriangle className="h-4 w-4" />;
      case 'FAIBLE':
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <Wine className="h-4 w-4" />;
    }
  };

  const filteredBoissons = stockBoissons.filter(boisson =>
    boisson.nom_boisson.toLowerCase().includes(searchTerm.toLowerCase()) ||
    boisson.categorie.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Chargement des stocks de boissons...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Stock Boissons</h2>
          <p className="text-gray-600">Gérez l'inventaire des boissons du bar</p>
        </div>
      </div>

      {/* Barre de recherche et filtres */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Rechercher une boisson..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <select
            value={selectedCategorie}
            onChange={(e) => setSelectedCategorie(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Toutes les catégories</option>
            {categories.map(cat => (
              <option key={cat} value={cat}>{cat}</option>
            ))}
          </select>

          <select
            value={selectedEmplacement}
            onChange={(e) => setSelectedEmplacement(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Tous les emplacements</option>
            {emplacements.map(emp => (
              <option key={emp} value={emp}>{emp}</option>
            ))}
          </select>

          <select
            value={selectedStatut}
            onChange={(e) => setSelectedStatut(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Tous les statuts</option>
            <option value="NORMAL">Normal</option>
            <option value="FAIBLE">Stock faible</option>
            <option value="CRITIQUE">Critique</option>
          </select>
        </div>
      </div>

      {/* Liste des boissons */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Boisson
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Catégorie
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Emplacement
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valeur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredBoissons.map((boisson) => (
                <tr key={boisson.stock_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {boisson.nom_boisson}
                      </div>
                      <div className="text-sm text-gray-500">
                        {boisson.type_conditionnement} - {boisson.volume_unitaire}L
                        {boisson.degre_alcool > 0 && (
                          <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-1 rounded">
                            {boisson.degre_alcool}°
                          </span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {boisson.categorie}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {boisson.quantite_actuelle} unités
                    </div>
                    <div className="text-xs text-gray-500">
                      Min: {boisson.quantite_minimale} | Max: {boisson.quantite_maximale}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatutColor(boisson.statut_stock)}`}>
                      {getStatutIcon(boisson.statut_stock)}
                      <span className="ml-1">{boisson.statut_stock}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-900">
                      <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                      {boisson.emplacement_stockage}
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <Thermometer className="h-3 w-3 mr-1" />
                      {boisson.temperature_stockage}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {boisson.valeur_stock_actuel.toFixed(0)} FCFA
                    </div>
                    <div className="text-xs text-gray-500">
                      Potentiel: {boisson.valeur_vente_potentielle.toFixed(0)} FCFA
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedBoisson(boisson);
                          setMouvementData({ ...mouvementData, type: 'ENTREE' });
                          setShowMouvementModal(true);
                        }}
                        className="text-green-600 hover:text-green-900"
                        title="Ajouter du stock"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedBoisson(boisson);
                          setMouvementData({ ...mouvementData, type: 'SORTIE' });
                          setShowMouvementModal(true);
                        }}
                        className="text-red-600 hover:text-red-900"
                        title="Retirer du stock"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal de mouvement de stock */}
      {showMouvementModal && selectedBoisson && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Mouvement de stock - {selectedBoisson.nom_boisson}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type de mouvement
                </label>
                <select
                  value={mouvementData.type}
                  onChange={(e) => setMouvementData({ ...mouvementData, type: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="ENTREE">Entrée</option>
                  <option value="SORTIE">Sortie</option>
                  <option value="AJUSTEMENT">Ajustement</option>
                  <option value="PERTE">Perte</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quantité (unités)
                </label>
                <input
                  type="number"
                  min="0"
                  step="1"
                  value={mouvementData.quantite}
                  onChange={(e) => setMouvementData({ ...mouvementData, quantite: parseInt(e.target.value) || 0 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (optionnel)
                </label>
                <textarea
                  value={mouvementData.notes}
                  onChange={(e) => setMouvementData({ ...mouvementData, notes: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Raison du mouvement..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowMouvementModal(false);
                  setSelectedBoisson(null);
                  setMouvementData({ type: 'ENTREE', quantite: 0, notes: '' });
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Annuler
              </button>
              <button
                onClick={handleMouvement}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Enregistrer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
