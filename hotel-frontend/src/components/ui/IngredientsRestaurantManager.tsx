import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import {
  Package,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import stockManagementService, { type StockDetaille } from '../../services/stockManagement.service';

interface IngredientsRestaurantManagerProps {
  complexeId: number;
}

export const IngredientsRestaurantManager: React.FC<IngredientsRestaurantManagerProps> = ({
  complexeId
}) => {
  const [ingredients, setIngredients] = useState<StockDetaille[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategorie, setSelectedCategorie] = useState<string>('');
  const [selectedStatut, setSelectedStatut] = useState<string>('');
  const [categories, setCategories] = useState<string[]>([]);
  const [showMouvementModal, setShowMouvementModal] = useState(false);
  const [selectedIngredient, setSelectedIngredient] = useState<StockDetaille | null>(null);
  const [mouvementData, setMouvementData] = useState({
    type: 'ENTREE' as 'ENTREE' | 'SORTIE' | 'AJUSTEMENT' | 'PERTE',
    quantite: 0,
    notes: ''
  });

  useEffect(() => {
    loadIngredients();
  }, [complexeId, selectedCategorie, selectedStatut]);

  const loadIngredients = async () => {
    try {
      setLoading(true);
      const response = await stockManagementService.getStockDetaille(complexeId, {
        categorie: selectedCategorie || undefined,
        statutStock: selectedStatut as any || undefined
      });
      
      setIngredients(response.ingredients);
      
      // Extraire les catégories uniques
      const uniqueCategories = [...new Set(response.ingredients.map(ing => ing.categorie))];
      setCategories(uniqueCategories);
    } catch (error: any) {
      toast.error(error.message || 'Erreur lors du chargement des ingrédients');
    } finally {
      setLoading(false);
    }
  };

  const handleMouvement = async () => {
    if (!selectedIngredient || mouvementData.quantite <= 0) {
      toast.error('Veuillez saisir une quantité valide');
      return;
    }

    try {
      await stockManagementService.enregistrerMouvement(
        selectedIngredient.ingredient_id,
        complexeId,
        mouvementData.type,
        mouvementData.quantite,
        {
          notes: mouvementData.notes
        }
      );

      toast.success('Mouvement enregistré avec succès');
      setShowMouvementModal(false);
      setSelectedIngredient(null);
      setMouvementData({ type: 'ENTREE', quantite: 0, notes: '' });
      loadIngredients();
    } catch (error: any) {
      toast.error(error.message || 'Erreur lors de l\'enregistrement du mouvement');
    }
  };

  const getStatutColor = (statut: string) => {
    switch (statut) {
      case 'CRITIQUE':
        return 'text-red-600 bg-red-100';
      case 'FAIBLE':
        return 'text-orange-600 bg-orange-100';
      case 'RUPTURE':
        return 'text-red-800 bg-red-200';
      case 'EXCESSIF':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-green-600 bg-green-100';
    }
  };

  const getStatutIcon = (statut: string) => {
    switch (statut) {
      case 'CRITIQUE':
      case 'RUPTURE':
        return <AlertTriangle className="h-4 w-4" />;
      case 'FAIBLE':
        return <TrendingDown className="h-4 w-4" />;
      case 'EXCESSIF':
        return <TrendingUp className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const filteredIngredients = ingredients.filter(ingredient =>
    ingredient.nom_ingredient.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ingredient.categorie.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Chargement des ingrédients...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête et filtres */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Ingrédients Restaurant</h2>
          <p className="text-gray-600">Gérez les stocks des ingrédients de cuisine</p>
        </div>
      </div>

      {/* Barre de recherche et filtres */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Rechercher un ingrédient..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <select
            value={selectedCategorie}
            onChange={(e) => setSelectedCategorie(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Toutes les catégories</option>
            {categories.map(cat => (
              <option key={cat} value={cat}>{cat}</option>
            ))}
          </select>

          <select
            value={selectedStatut}
            onChange={(e) => setSelectedStatut(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Tous les statuts</option>
            <option value="NORMAL">Normal</option>
            <option value="FAIBLE">Stock faible</option>
            <option value="CRITIQUE">Critique</option>
            <option value="RUPTURE">Rupture</option>
            <option value="EXCESSIF">Excessif</option>
          </select>
        </div>
      </div>

      {/* Liste des ingrédients */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ingrédient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Catégorie
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock Actuel
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valeur
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredIngredients.map((ingredient) => (
                <tr key={ingredient.ingredient_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {ingredient.nom_ingredient}
                      </div>
                      <div className="text-sm text-gray-500">
                        {ingredient.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {ingredient.categorie}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {ingredient.stock_actuel} {ingredient.unite_mesure}
                    </div>
                    <div className="text-xs text-gray-500">
                      Min: {ingredient.stock_minimal} | Max: {ingredient.stock_maximal}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatutColor(ingredient.statut_stock)}`}>
                      {getStatutIcon(ingredient.statut_stock)}
                      <span className="ml-1">{ingredient.statut_stock}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {ingredient.valeur_stock_actuel.toFixed(0)} FCFA
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedIngredient(ingredient);
                          setMouvementData({ ...mouvementData, type: 'ENTREE' });
                          setShowMouvementModal(true);
                        }}
                        className="text-green-600 hover:text-green-900"
                        title="Ajouter du stock"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedIngredient(ingredient);
                          setMouvementData({ ...mouvementData, type: 'SORTIE' });
                          setShowMouvementModal(true);
                        }}
                        className="text-red-600 hover:text-red-900"
                        title="Retirer du stock"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal de mouvement de stock */}
      {showMouvementModal && selectedIngredient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Mouvement de stock - {selectedIngredient.nom_ingredient}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type de mouvement
                </label>
                <select
                  value={mouvementData.type}
                  onChange={(e) => setMouvementData({ ...mouvementData, type: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="ENTREE">Entrée</option>
                  <option value="SORTIE">Sortie</option>
                  <option value="AJUSTEMENT">Ajustement</option>
                  <option value="PERTE">Perte</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quantité ({selectedIngredient.unite_mesure})
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.1"
                  value={mouvementData.quantite}
                  onChange={(e) => setMouvementData({ ...mouvementData, quantite: parseFloat(e.target.value) || 0 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (optionnel)
                </label>
                <textarea
                  value={mouvementData.notes}
                  onChange={(e) => setMouvementData({ ...mouvementData, notes: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Raison du mouvement..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowMouvementModal(false);
                  setSelectedIngredient(null);
                  setMouvementData({ type: 'ENTREE', quantite: 0, notes: '' });
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Annuler
              </button>
              <button
                onClick={handleMouvement}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Enregistrer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
