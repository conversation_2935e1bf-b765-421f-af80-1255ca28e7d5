const fetch = require('./backend/node_modules/node-fetch');

async function testLogin() {
  try {
    // Essayer de se connecter avec l'admin du complexe 1
    const loginData = {
      email: '<EMAIL>',
      password: 'password123', // Mot de passe par défaut probable
      userType: 'admin_complexe'
    };
    
    console.log('Tentative de connexion avec:', loginData.email);
    
    const response = await fetch('http://localhost:8080/api/auth/admin-complexe/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(loginData)
    });
    
    const result = await response.json();
    
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (result.token) {
      console.log('\n✅ Connexion réussie !');
      console.log('Token:', result.token);
      
      // Tester l'import avec ce token
      await testImportWithToken(result.token);
    } else {
      console.log('\n❌ Connexion échouée');
      
      // Essayer avec d'autres mots de passe courants
      const commonPasswords = ['admin123', 'cacaveli123', '123456', 'admin'];
      
      for (const password of commonPasswords) {
        console.log(`\nEssai avec le mot de passe: ${password}`);
        const testData = { ...loginData, password };
        
        const testResponse = await fetch('http://localhost:8080/api/auth/admin-complexe/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testData)
        });
        
        const testResult = await testResponse.json();
        
        if (testResult.token) {
          console.log('✅ Connexion réussie avec:', password);
          console.log('Token:', testResult.token);
          await testImportWithToken(testResult.token);
          return;
        }
      }
    }
    
  } catch (error) {
    console.error('Erreur lors du test de connexion:', error.message);
  }
}

async function testImportWithToken(token) {
  try {
    const fs = require('fs');
    const FormData = require('./backend/node_modules/form-data');
    
    console.log('\n=== Test d\'import avec token ===');
    
    // Lire le fichier Excel
    const fileBuffer = fs.readFileSync('../test_ingredients.xlsx');
    
    // Créer FormData
    const formData = new FormData();
    formData.append('file', fileBuffer, {
      filename: 'test_ingredients.xlsx',
      contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    
    // Envoyer la requête à l'API avec le token
    const response = await fetch('http://localhost:8080/api/ingredient-import/cuisine/1', {
      method: 'POST',
      body: formData,
      headers: {
        ...formData.getHeaders(),
        'Authorization': `Bearer ${token}`
      }
    });
    
    const result = await response.json();
    
    console.log('Import Status:', response.status);
    console.log('Import Response:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ Import réussi !');
      console.log(`Total: ${result.data.totalRows}`);
      console.log(`Succès: ${result.data.successCount}`);
      console.log(`Erreurs: ${result.data.errorCount}`);
    } else {
      console.log('\n❌ Import échoué');
    }
    
  } catch (error) {
    console.error('Erreur lors du test d\'import:', error.message);
  }
}

testLogin();
